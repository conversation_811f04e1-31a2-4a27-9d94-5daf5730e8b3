function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/ar-TN-hXpJlHQz.js","assets/languages-BtQEISXM.js","assets/be-BY-CNG3QvRr.js","assets/ca-ES-DaFbzlsN.js","assets/da-DK-BOlqBLAC.js","assets/de-DE-D8j2hVsF.js","assets/el-GR-CPghj_w6.js","assets/en-GB-C7JHOrHG.js","assets/en-US-B0s0BWzt.js","assets/es-ES-DBc5Zs2w.js","assets/fr-FR-B9l2OUfe.js","assets/he-IL-eg_IqJCK.js","assets/id-ID-B0V93tcH.js","assets/it-IT-CT3hgywc.js","assets/ja-JP-9Mx_HtSv.js","assets/ko-KR-CxGXyVbz.js","assets/nl-NL-BPPnxxwY.js","assets/no-NO-DiHwyl5f.js","assets/pl-PL-C9v2eRNf.js","assets/pt-BR-Dd28HDO_.js","assets/ru-RU-ClHujSzv.js","assets/uk-UA-BbOKxyWq.js","assets/zh-CN-Ce7uxnjN.js"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))i(s);new MutationObserver(s=>{for(const a of s)if(a.type==="childList")for(const o of a.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&i(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const a={};return s.integrity&&(a.integrity=s.integrity),s.referrerPolicy&&(a.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?a.credentials="include":s.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function i(s){if(s.ep)return;s.ep=!0;const a=n(s);fetch(s.href,a)}})();const Ut="modulepreload",qt=function(t){return"/"+t},lt={},p=function(e,n,i){let s=Promise.resolve();if(n&&n.length>0){const a=document.getElementsByTagName("link");s=Promise.all(n.map(o=>{if(o=qt(o),o in lt)return;lt[o]=!0;const r=o.endsWith(".css"),c=r?'[rel="stylesheet"]':"";if(!!i)for(let l=a.length-1;l>=0;l--){const u=a[l];if(u.href===o&&(!r||u.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${c}`))return;const h=document.createElement("link");if(h.rel=r?"stylesheet":Ut,r||(h.as="script",h.crossOrigin=""),h.href=o,document.head.appendChild(h),r)return new Promise((l,u)=>{h.addEventListener("load",l),h.addEventListener("error",()=>u(new Error(`Unable to preload CSS for ${o}`)))})}))}return s.then(()=>e()).catch(a=>{const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=a,window.dispatchEvent(o),!o.defaultPrevented)throw a})},I=document.querySelector("canvas"),fi=document.querySelector(".menu"),vi=document.querySelector("main"),dt=document.querySelector(".details"),A=document.querySelector(".posterize"),Wt=document.querySelector("[for=posterize]"),E=document.querySelector(".color"),Ft=document.querySelector("[for=color]"),T=document.querySelector(".monochrome"),Gt=document.querySelector("[for=monochrome]"),V=document.querySelector(".consider-dpr"),Yt=document.querySelector('[for="consider-dpr"]'),P=document.querySelector(".optimize-curves"),Xt=document.querySelector('[for="optimize-curves"]'),G=document.querySelector(".show-advanced"),Kt=document.querySelector('[for="show-advanced"]'),w=document.querySelector("img"),it=document.querySelector(".reset-all"),Ee=document.querySelector(".open"),ke=document.querySelector(".save"),Se=document.querySelector(".copy"),xe=document.querySelector(".share"),Ce=document.querySelector(".paste"),Te=document.querySelector(".install"),y=document.querySelector(".svg-output"),ue=document.querySelector(".debug"),Pe=document.querySelector("progress"),Z=document.querySelector(".toast"),tt=document.querySelector("details.main"),Be=document.querySelector("summary"),St=document.querySelector(".close-options-button"),Jt=document.querySelector(".license"),Zt=document.querySelector(".about"),re=document.querySelector("pinch-zoom"),Oe=document.querySelector(".language"),xt=document.querySelector("dark-mode-toggle"),R=document.documentElement,Qt=document.querySelector("meta[name=theme-color]"),Q=window.devicePixelRatio;re.addEventListener("change",()=>{const{x:t,y:e,scale:n}=re;y.setAttribute("transform",`translate(${t}, ${e}) scale(${n})`)});re.addEventListener("pointerdown",()=>{re.style.cursor="grabbing"});re.addEventListener("pointerup",()=>{re.style.cursor=""});const Ct=()=>{y.setAttribute("transform","")},Re=(t,e)=>{let n;return function(...s){const a=()=>{clearTimeout(n),t(...s)};clearTimeout(n),n=setTimeout(a,e)}},en=/Mac|iPhone/.test(navigator.platform),tn=/Version\/.* Safari\//.test(navigator.userAgent);/*!
 * canvas-size
 * v2.0.0
 * https://github.com/jhildenbiddle/canvas-size
 * (c) 2015-2024 John Hildenbiddle <http://hildenbiddle.com>
 * MIT license
 */function pe(t){const e=t.sizes.shift(),n=Math.max(Math.ceil(e[0]),1),i=Math.max(Math.ceil(e[1]),1),s=[n-1,i-1,1,1],a=performance.now(),o=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope;let r,c;o?(r=new OffscreenCanvas(1,1),c=new OffscreenCanvas(n,i)):(r=document.createElement("canvas"),r.width=1,r.height=1,c=document.createElement("canvas"),c.width=n,c.height=i);const d=r.getContext("2d"),h=c.getContext("2d");h&&(h.fillRect.apply(h,s),d.drawImage(c,n-1,i-1,1,1,0,0,1,1));const l=d&&d.getImageData(0,0,1,1).data[3]!==0,u=parseInt(performance.now()-a);return[r,c].forEach(v=>{v.height=0,v.width=0}),o?(postMessage({width:n,height:i,testTime:u,isTestPass:l}),!l&&t.sizes.length&&setTimeout(()=>{pe(t)},0)):l?t.onSuccess({width:n,height:i,testTime:u}):(t.onError({width:n,height:i,testTime:u}),t.sizes.length&&setTimeout(()=>{pe(t)},0)),l}const He={area:[16384,14188,11402,11180,10836,8192,4096,1],height:[8388607,65535,32767,16384,8192,4096,1],width:[4194303,65535,32767,16384,8192,4096,1]},ee={max:null,min:1,sizes:[],step:1024,useWorker:!1,onError:Function.prototype,onSuccess:Function.prototype},ae={};function Ne(t){const e=t.width===t.height,n=t.height===1,i=t.width===1,s=[];if(!t.width||!t.height)t.sizes.forEach(a=>{const o=e||n?a:1,r=e||i?a:1;s.push([o,r])});else{const a=t.min||ee.min,o=t.step||ee.step;let r=Math.max(t.width,t.height);for(;r>=a;){const c=e||n?r:1,d=e||i?r:1;s.push([c,d]),r-=o}}return s}function we(t){const e=typeof window<"u",n=e&&"Promise"in window,i=e&&"HTMLCanvasElement"in window,s=e&&"OffscreenCanvas"in window,a=URL.createObjectURL(new Blob([])).slice(-36),o=performance.now(),{onError:r,onSuccess:c,...d}=t,h=()=>parseInt(performance.now()-o);let l=null;if(!i)return!1;if(t.useWorker&&s){const u=`
            var canvasTest = ${pe.toString()};
            onmessage = function(e) {
                canvasTest(e.data);
            };
        `,v=new Blob([u],{type:"application/javascript"}),_=URL.createObjectURL(v);l=new Worker(_),URL.revokeObjectURL(_),l.onmessage=function(C){const{width:X,height:se,testTime:U,isTestPass:oe}=C.data,ve={width:X,height:se,testTime:U,totalTime:h()};oe?(ae[a].onSuccess(ve),delete ae[a]):ae[a].onError(ve)}}if(n)return new Promise(u=>{const v={...t,onError(_){let{width:C,height:X,testTime:se}=_;const U={width:C,height:X,testTime:se,totalTime:h()};let oe;if(t.sizes.length===0)oe=!0;else{const[[ve,Nt]]=t.sizes.slice(-1);oe=C===ve&&X===Nt}r(U),oe&&u({...U,success:!1})},onSuccess(_){let{width:C,height:X,testTime:se}=_;const U={width:C,height:X,testTime:se,totalTime:h()};c(U),u({...U,success:!0})}};if(l){const{onError:_,onSuccess:C}=v;ae[a]={onError:_,onSuccess:C},l.postMessage(d)}else pe(v)});if(l)ae[a]={onError:r,onSuccess:c},l.postMessage(d);else return pe(t)}const nn={maxArea(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e=Ne({width:t.max,height:t.max,min:t.min,step:t.step,sizes:[...He.area]}),n={...ee,...t,sizes:e};return we(n)},maxHeight(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e=Ne({width:1,height:t.max,min:t.min,step:t.step,sizes:[...He.height]}),n={...ee,...t,sizes:e};return we(n)},maxWidth(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e=Ne({width:t.max,height:1,min:t.min,step:t.step,sizes:[...He.width]}),n={...ee,...t,sizes:e};return we(n)},test(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e={...ee,...t};return e.sizes=[...e.sizes],e.width&&e.height&&(e.sizes=[[e.width,e.height]]),we(e)}};let Tt,Pt;const Rt="OffscreenCanvas"in window&&"CanvasFilter"in window;if(Rt)p(()=>import("./preprocessworker-CehXHJLM.js"),__vite__mapDeps([])).then(t=>{const e=t.default;let n=null;const i=I.getContext("2d");Tt=async()=>{n&&n.terminate(),n=new e;const s=I.cloneNode().transferControlToOffscreen();return n.postMessage({offscreen:s},[s]),new Promise(async a=>{const{width:o,height:r}=ht();let c;try{c=await createImageBitmap(w)}catch{try{c=await createImageBitmap(w,0,0,o,r)}catch(h){console.error(h.name,h.message),y.innerHTML="",x(h.message);return}}const d=new MessageChannel;d.port1.onmessage=({data:h})=>{d.port1.close(),n&&(n.terminate(),n=null),I.width=o,I.height=r,i.putImageData(h.result,0,0),a(h.result)},n.postMessage({inputImageBitmap:c,posterize:A.checked,rgba:{r:H(g[L.red]),g:H(g[L.green]),b:H(g[L.blue]),a:H(g[L.alpha])},cssFilters:At(),rotate:Number(g[ge.rotation].value),width:o,height:r,dpr:Q},[d.port2])})}});else{const t=I.getContext("2d",{desynchronized:!0});t.scale(Q,Q),t.imageSmoothingEnabled=!0,Pt=()=>{let{width:e,height:n}=ht();const i=V.checked?Q:1;let s=1;for(;!nn.test({width:e,height:n});)e=Math.floor(e/2),n=Math.floor(n/2),s/=2;I.width=e,I.height=n,t.clearRect(0,0,e,n),t.setTransform(1,0,0,1,e/2,n/2),t.drawImage(w,-i*w.naturalWidth*s/2,-i*w.naturalHeight*s/2);const a=Number(g[ge.rotation].value);t.rotate(a*Math.PI/180),t.filter=sn();const o=t.getImageData(0,0,I.width,I.height),r=g[L.red].value,c=g[L.green].value,d=g[L.blue].value,h=g[L.alpha].value+1;for(let l=0;l<o.data.length;l+=4)o.data[l]=Math.floor(o.data[l]/255*(r-1))*(255/(r-1)),o.data[l+1]=Math.floor(o.data[l+1]/255*(c-1))*(255/(c-1)),o.data[l+2]=Math.floor(o.data[l+2]/255*(d-1))*(255/(d-1)),o.data[l+3]=Math.floor(o.data[l+3]/255*(h-1))*(255/(h-1));return t.putImageData(o,0,0),o}}const ht=()=>{const t=Number(g[ge.scale].value)/100;return{width:Math.ceil(Q*w.naturalWidth*t),height:Math.ceil(Q*w.naturalHeight*t)}},H=t=>{const e=Number(t.value),n=[];for(let i=0;i<=e;i++)n[i]=Number((1/e*i).toFixed(1));return n},rn=()=>`data:image/svg+xml;utf8,<svg
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <filter id="posterize">
        <feComponentTransfer>
          <feFuncR type="discrete" tableValues="${H(g[L.red]).join(",")}" />
          <feFuncG type="discrete" tableValues="${H(g[L.green]).join(",")}" />
          <feFuncB type="discrete" tableValues="${H(g[L.blue]).join(",")}" />
          <feFuncA type="discrete" tableValues="${H(g[L.alpha])}" />
        </feComponentTransfer>
      </filter>
    </svg>`.replace(/[\r\n]/g,"").replace(/\s+/g," ").trim(),At=()=>{let t="";for(const[e,n]of Object.entries($t)){const i=g[e];n.initial!==Number(i.value)&&(t+=`${e}(${i.value}${i.dataset.unit}) `)}return t},sn=()=>{let t=`${A.checked?`url('${rn()}#posterize') `:""}`;return t+=At(),t.trim()||"none"};function on(t){return new Worker("/assets/monochromeworker-CYWnkG-H.js",{name:t?.name})}let q=null;const an=async t=>(q&&q.terminate(),q=new on,new Promise(async e=>{const n=new MessageChannel;n.port1.onmessage=({data:s})=>{n.port1.close(),q&&(q.terminate(),q=null),e(s.result)};const i={turdsize:Number(g[k.turdsize].value),alphamax:Number(g[k.alphamax].value),turnpolicy:Number(g[k.turnpolicy].value),opttolerance:Number(g[k.opttolerance].value),opticurve:P.checked?1:0,extractcolors:!1};q.postMessage({imageData:t,params:i},[n.port2])}));function cn(t){return new Worker("/assets/colorworker-CnMNwOdv.js",{name:t?.name})}let W=null;const $={},ln=async t=>(W&&W.terminate(),W=new cn,new Promise(async e=>{const n=new MessageChannel;n.port1.onmessage=({data:d})=>{n.port1.close(),W&&(W.terminate(),W=null),e(d.result)},Pe.value=0;let i="",s="",a="",o=0;$.current&&(clearInterval($.current),$.current=null),$.current=setInterval(()=>{const d=`${i}${a}${s}`;if(d.length!==o){const h=y.dataset.transform;h&&y.setAttribute("transform",h),y.innerHTML=d,o=d.length}},500);const r=new MessageChannel;r.port1.onmessage=({data:d})=>{const h=Math.floor(d.processed/d.total*100);if(Pe.value=h,d.svg){i||(i=d.svg.replace(/(.*?<svg[^>]+>)(.*?)(<\/svg>)/,"$1").replace(/\s+width="\d+(?:\.\d+)?"/,"").replace(/\s+height="\d+(?:\.\d+)"/,""),s=d.svg.replace(/(.*?<svg[^>]+>)(.*?)(<\/svg>)/,"$3"));const l=d.svg.replace(/(.*?<svg[^>]+>)(.*?)(<\/svg>)/,"$2");a+=l}d.processed===d.total&&(clearInterval($.current),$.current=null,r.port1.close(),Pe.value=0)};const c={minPathSegments:Number(g[k.minPathLength].value),strokeWidth:Number(g[k.strokeWidth].value),turdsize:Number(g[k.turdsize].value),alphamax:Number(g[k.alphamax].value),turnpolicy:Number(g[k.turnpolicy].value),opttolerance:Number(g[k.opttolerance].value),opticurve:P.checked?1:0,extractcolors:!1,posterizelevel:2,posterizationalgorithm:0};W.postMessage({imageData:t,params:c},[n.port2,r.port2])})),ut=(t,e)=>{const n=t[e];return n?typeof n=="function"?n():Promise.resolve(n):new Promise((i,s)=>{(typeof queueMicrotask=="function"?queueMicrotask:setTimeout)(s.bind(null,new Error("Unknown variable dynamic import: "+e)))})},ye="language",dn=["ar","be","ca","da","de","el","en","es","fr","he","id","it","ja","ko","nl","no","pl","pt","ru","uk","zh"],hn=["ar-TN","be-BY","ca-ES","da-DK","de-DE","el-GR","en-GB","en-US","es-ES","fr-FR","he-IL","id-ID","it-IT","ja-JP","ko-KR","nl-NL","no-NO","pl-PL","pt-BR","ru-RU","uk-UA","zh-CN"],un=["ar","fa","he","ur"];class pn{constructor(){this.defaultLanguage="en",this.defaultLocale="US",this.translations=null,this.supportedLanguages=dn,this.supportedLocales=hn,this.currentLanguageAndLocale=this.detectOrRestoreLanguageAndLocale()}findBestMatchingLanguageAndLocale(e,n){return n&&(n=n.toUpperCase()),!e||!this.supportedLanguages.includes(e)?(e=this.defaultLanguage,n=this.defaultLocale):(!n||!this.supportedLocales.includes(`${e}-${n}`))&&(n=this.supportedLocales.find(i=>i.startsWith(`${e}-`)).split("-")[1]),{language:e,locale:n}}detectOrRestoreLanguageAndLocale(){const e=new URL(location),n=e.searchParams,i=n.get("lang");if(i){n.delete("lang"),history.pushState({},"",e);const[c,d=""]=i.split("-"),{language:h,locale:l}=this.findBestMatchingLanguageAndLocale(c,d);return this.setLanguageAndLocale(h,l),{language:h,locale:l}}try{const c=localStorage.getItem(ye);if(c){const{language:d,locale:h}=JSON.parse(c);return this.setLanguageAndLocale(d,h),{language:d,locale:h}}}catch{}const[s,a=""]=navigator.language?.split("-"),{language:o,locale:r}=this.findBestMatchingLanguageAndLocale(s,a);return this.setLanguageAndLocale(o,r),{language:o,locale:r}}async setLanguageAndLocale(e,n){try{this.supportedLanguages.includes(e)||(localStorage.removeItem(ye),e=this.defaultLanguage,n=this.defaultLocale),n&&!this.supportedLocales.includes(`${e}-${n}`)&&(localStorage.removeItem(ye),e=this.defaultLanguage,n=this.defaultLocale),this.currentLanguageAndLocale={language:e,locale:n},localStorage.setItem(ye,JSON.stringify(this.currentLanguageAndLocale))}catch{}R.lang=`${e}${n?`-${n}`:""}`,un.includes(e)?R.dir="rtl":R.dir="ltr",await this.getTranslations()}async getTranslations(){const{language:e,locale:n}=this.currentLanguageAndLocale;this.translations=(await ut(Object.assign({"../i18n/ar-TN.js":()=>p(()=>import("./ar-TN-hXpJlHQz.js"),__vite__mapDeps([0,1])),"../i18n/be-BY.js":()=>p(()=>import("./be-BY-CNG3QvRr.js"),__vite__mapDeps([2,1])),"../i18n/ca-ES.js":()=>p(()=>import("./ca-ES-DaFbzlsN.js"),__vite__mapDeps([3,1])),"../i18n/da-DK.js":()=>p(()=>import("./da-DK-BOlqBLAC.js"),__vite__mapDeps([4,1])),"../i18n/de-DE.js":()=>p(()=>import("./de-DE-D8j2hVsF.js"),__vite__mapDeps([5,1])),"../i18n/el-GR.js":()=>p(()=>import("./el-GR-CPghj_w6.js"),__vite__mapDeps([6,1])),"../i18n/en-GB.js":()=>p(()=>import("./en-GB-C7JHOrHG.js"),__vite__mapDeps([7,1])),"../i18n/en-US.js":()=>p(()=>import("./en-US-B0s0BWzt.js"),__vite__mapDeps([8,1])),"../i18n/es-ES.js":()=>p(()=>import("./es-ES-DBc5Zs2w.js"),__vite__mapDeps([9,1])),"../i18n/fr-FR.js":()=>p(()=>import("./fr-FR-B9l2OUfe.js"),__vite__mapDeps([10,1])),"../i18n/he-IL.js":()=>p(()=>import("./he-IL-eg_IqJCK.js"),__vite__mapDeps([11,1])),"../i18n/id-ID.js":()=>p(()=>import("./id-ID-B0V93tcH.js"),__vite__mapDeps([12,1])),"../i18n/it-IT.js":()=>p(()=>import("./it-IT-CT3hgywc.js"),__vite__mapDeps([13,1])),"../i18n/ja-JP.js":()=>p(()=>import("./ja-JP-9Mx_HtSv.js"),__vite__mapDeps([14,1])),"../i18n/ko-KR.js":()=>p(()=>import("./ko-KR-CxGXyVbz.js"),__vite__mapDeps([15,1])),"../i18n/languages.js":()=>p(()=>import("./languages-BtQEISXM.js"),__vite__mapDeps([])),"../i18n/nl-NL.js":()=>p(()=>import("./nl-NL-BPPnxxwY.js"),__vite__mapDeps([16,1])),"../i18n/no-NO.js":()=>p(()=>import("./no-NO-DiHwyl5f.js"),__vite__mapDeps([17,1])),"../i18n/pl-PL.js":()=>p(()=>import("./pl-PL-C9v2eRNf.js"),__vite__mapDeps([18,1])),"../i18n/pt-BR.js":()=>p(()=>import("./pt-BR-Dd28HDO_.js"),__vite__mapDeps([19,1])),"../i18n/ru-RU.js":()=>p(()=>import("./ru-RU-ClHujSzv.js"),__vite__mapDeps([20,1])),"../i18n/uk-UA.js":()=>p(()=>import("./uk-UA-BbOKxyWq.js"),__vite__mapDeps([21,1])),"../i18n/zh-CN.js":()=>p(()=>import("./zh-CN-Ce7uxnjN.js"),__vite__mapDeps([22,1]))}),`../i18n/${e}${n?`-${n}`:""}.js`).catch(()=>ut(Object.assign({"../i18n/ar-TN.js":()=>p(()=>import("./ar-TN-hXpJlHQz.js"),__vite__mapDeps([0,1])),"../i18n/be-BY.js":()=>p(()=>import("./be-BY-CNG3QvRr.js"),__vite__mapDeps([2,1])),"../i18n/ca-ES.js":()=>p(()=>import("./ca-ES-DaFbzlsN.js"),__vite__mapDeps([3,1])),"../i18n/da-DK.js":()=>p(()=>import("./da-DK-BOlqBLAC.js"),__vite__mapDeps([4,1])),"../i18n/de-DE.js":()=>p(()=>import("./de-DE-D8j2hVsF.js"),__vite__mapDeps([5,1])),"../i18n/el-GR.js":()=>p(()=>import("./el-GR-CPghj_w6.js"),__vite__mapDeps([6,1])),"../i18n/en-GB.js":()=>p(()=>import("./en-GB-C7JHOrHG.js"),__vite__mapDeps([7,1])),"../i18n/en-US.js":()=>p(()=>import("./en-US-B0s0BWzt.js"),__vite__mapDeps([8,1])),"../i18n/es-ES.js":()=>p(()=>import("./es-ES-DBc5Zs2w.js"),__vite__mapDeps([9,1])),"../i18n/fr-FR.js":()=>p(()=>import("./fr-FR-B9l2OUfe.js"),__vite__mapDeps([10,1])),"../i18n/he-IL.js":()=>p(()=>import("./he-IL-eg_IqJCK.js"),__vite__mapDeps([11,1])),"../i18n/id-ID.js":()=>p(()=>import("./id-ID-B0V93tcH.js"),__vite__mapDeps([12,1])),"../i18n/it-IT.js":()=>p(()=>import("./it-IT-CT3hgywc.js"),__vite__mapDeps([13,1])),"../i18n/ja-JP.js":()=>p(()=>import("./ja-JP-9Mx_HtSv.js"),__vite__mapDeps([14,1])),"../i18n/ko-KR.js":()=>p(()=>import("./ko-KR-CxGXyVbz.js"),__vite__mapDeps([15,1])),"../i18n/languages.js":()=>p(()=>import("./languages-BtQEISXM.js"),__vite__mapDeps([])),"../i18n/nl-NL.js":()=>p(()=>import("./nl-NL-BPPnxxwY.js"),__vite__mapDeps([16,1])),"../i18n/no-NO.js":()=>p(()=>import("./no-NO-DiHwyl5f.js"),__vite__mapDeps([17,1])),"../i18n/pl-PL.js":()=>p(()=>import("./pl-PL-C9v2eRNf.js"),__vite__mapDeps([18,1])),"../i18n/pt-BR.js":()=>p(()=>import("./pt-BR-Dd28HDO_.js"),__vite__mapDeps([19,1])),"../i18n/ru-RU.js":()=>p(()=>import("./ru-RU-ClHujSzv.js"),__vite__mapDeps([20,1])),"../i18n/uk-UA.js":()=>p(()=>import("./uk-UA-BbOKxyWq.js"),__vite__mapDeps([21,1])),"../i18n/zh-CN.js":()=>p(()=>import("./zh-CN-Ce7uxnjN.js"),__vite__mapDeps([22,1]))}),`../i18n/${this.defaultLocale}.js`))).default}t(e){return this.translations[e]||"⛔️ Missing translation"}}const m=new pn,mn='<svg viewBox="0 0 57 57" xmlns="http://www.w3.org/2000/svg" stroke="currentColor"><g transform="translate(1 1)" stroke-width="2" fill="none" fill-rule="evenodd"><circle cx="5" cy="50" r="5"><animate attributeName="cy" begin="0s" dur="2.2s" values="50;5;50;50" calcMode="linear" repeatCount="indefinite"/><animate attributeName="cx" begin="0s" dur="2.2s" values="5;27;49;5" calcMode="linear" repeatCount="indefinite"/></circle><circle cx="27" cy="5" r="5"><animate attributeName="cy" begin="0s" dur="2.2s" from="5" to="5" values="5;50;50;5" calcMode="linear" repeatCount="indefinite"/><animate attributeName="cx" begin="0s" dur="2.2s" from="27" to="27" values="27;49;5;27" calcMode="linear" repeatCount="indefinite"/></circle><circle cx="49" cy="50" r="5"><animate attributeName="cy" begin="0s" dur="2.2s" values="50;50;5;50" calcMode="linear" repeatCount="indefinite"/><animate attributeName="cx" from="49" to="49" begin="0s" dur="2.2s" values="49;5;27;49" calcMode="linear" repeatCount="indefinite"/></circle></g></svg>',gn=t=>{if(t===0)return"0B";const e=Math.floor(Math.log(t)/Math.log(1024));return`${(t/Math.pow(1024,e)).toFixed(2)*1} ${["B","KB","MB"][e]}`},pt=(t,e)=>{t&&(t=t.replace(/\s+width="\d+(?:\.\d+)?"/,"").replace(/\s+height="\d+(?:\.\d+)"/,""),y.classList.remove(Me),y.classList.remove($e),y.classList.add(e),y.innerHTML=t,x(`${m.t("svgSize")}: ${gn(t.length)}`,3e3))},O=async()=>{y.innerHTML="",y.classList.remove(Me,$e),$.current&&(clearInterval($.current),$.current=null);const t=y.getAttribute("transform");y.innerHTML=mn,t&&(y.dataset.transform=t,y.setAttribute("transform",""));const e=Rt?await Tt():Pt();if(E.checked){const n=await ln(e);t&&y.setAttribute("transform",t),pt(n,Me)}else{const n=await an(e);t&&y.setAttribute("transform",t),pt(n,$e)}},fe=(()=>{if(typeof self>"u")return!1;if("top"in self&&self!==top)try{top.window.document._=0}catch{return!1}return"showOpenFilePicker"in self})(),fn=fe?Promise.resolve().then(function(){return Ln}):Promise.resolve().then(function(){return Tn});async function vn(...t){return(await fn).default(...t)}fe?Promise.resolve().then(function(){return kn}):Promise.resolve().then(function(){return Rn});const wn=fe?Promise.resolve().then(function(){return xn}):Promise.resolve().then(function(){return In});async function yn(...t){return(await wn).default(...t)}const _n=async t=>{const e=await t.getFile();return e.handle=t,e};var bn=async(t=[{}])=>{Array.isArray(t)||(t=[t]);const e=[];t.forEach((s,a)=>{e[a]={description:s.description||"Files",accept:{}},s.mimeTypes?s.mimeTypes.map(o=>{e[a].accept[o]=s.extensions||[]}):e[a].accept["*/*"]=s.extensions||[]});const n=await window.showOpenFilePicker({id:t[0].id,startIn:t[0].startIn,types:e,multiple:t[0].multiple||!1,excludeAcceptAllOption:t[0].excludeAcceptAllOption||!1}),i=await Promise.all(n.map(_n));return t[0].multiple?i:i[0]},Ln={__proto__:null,default:bn};function Ae(t){function e(n){if(Object(n)!==n)return Promise.reject(new TypeError(n+" is not an object."));var i=n.done;return Promise.resolve(n.value).then(function(s){return{value:s,done:i}})}return Ae=function(n){this.s=n,this.n=n.next},Ae.prototype={s:null,n:null,next:function(){return e(this.n.apply(this.s,arguments))},return:function(n){var i=this.s.return;return i===void 0?Promise.resolve({value:n,done:!0}):e(i.apply(this.s,arguments))},throw:function(n){var i=this.s.return;return i===void 0?Promise.reject(n):e(i.apply(this.s,arguments))}},new Ae(t)}const It=async(t,e,n=t.name,i)=>{const s=[],a=[];var o,r=!1,c=!1;try{for(var d,h=function(l){var u,v,_,C=2;for(typeof Symbol<"u"&&(v=Symbol.asyncIterator,_=Symbol.iterator);C--;){if(v&&(u=l[v])!=null)return u.call(l);if(_&&(u=l[_])!=null)return new Ae(u.call(l));v="@@asyncIterator",_="@@iterator"}throw new TypeError("Object is not async iterable")}(t.values());r=!(d=await h.next()).done;r=!1){const l=d.value,u=`${n}/${l.name}`;l.kind==="file"?a.push(l.getFile().then(v=>(v.directoryHandle=t,v.handle=l,Object.defineProperty(v,"webkitRelativePath",{configurable:!0,enumerable:!0,get:()=>u})))):l.kind!=="directory"||!e||i&&i(l)||s.push(It(l,e,u,i))}}catch(l){c=!0,o=l}finally{try{r&&h.return!=null&&await h.return()}finally{if(c)throw o}}return[...(await Promise.all(s)).flat(),...await Promise.all(a)]};var En=async(t={})=>{t.recursive=t.recursive||!1,t.mode=t.mode||"read";const e=await window.showDirectoryPicker({id:t.id,startIn:t.startIn,mode:t.mode});return(await(await e.values()).next()).done?[e]:It(e,t.recursive,void 0,t.skipDirectory)},kn={__proto__:null,default:En},Sn=async(t,e=[{}],n=null,i=!1,s=null)=>{Array.isArray(e)||(e=[e]),e[0].fileName=e[0].fileName||"Untitled";const a=[];let o=null;if(t instanceof Blob&&t.type?o=t.type:t.headers&&t.headers.get("content-type")&&(o=t.headers.get("content-type")),e.forEach((d,h)=>{a[h]={description:d.description||"Files",accept:{}},d.mimeTypes?(h===0&&o&&d.mimeTypes.push(o),d.mimeTypes.map(l=>{a[h].accept[l]=d.extensions||[]})):o?a[h].accept[o]=d.extensions||[]:a[h].accept["*/*"]=d.extensions||[]}),n)try{await n.getFile()}catch(d){if(n=null,i)throw d}const r=n||await window.showSaveFilePicker({suggestedName:e[0].fileName,id:e[0].id,startIn:e[0].startIn,types:a,excludeAcceptAllOption:e[0].excludeAcceptAllOption||!1});!n&&s&&s(r);const c=await r.createWritable();return"stream"in t?(await t.stream().pipeTo(c),r):"body"in t?(await t.body.pipeTo(c),r):(await c.write(await t),await c.close(),r)},xn={__proto__:null,default:Sn},Cn=async(t=[{}])=>(Array.isArray(t)||(t=[t]),new Promise((e,n)=>{const i=document.createElement("input");i.type="file";const s=[...t.map(c=>c.mimeTypes||[]),...t.map(c=>c.extensions||[])].join();i.multiple=t[0].multiple||!1,i.accept=s||"",i.style.display="none",document.body.append(i);const a=c=>{typeof o=="function"&&o(),e(c)},o=t[0].legacySetup&&t[0].legacySetup(a,()=>o(n),i),r=()=>{window.removeEventListener("focus",r),i.remove()};i.addEventListener("click",()=>{window.addEventListener("focus",r)}),i.addEventListener("change",()=>{window.removeEventListener("focus",r),i.remove(),a(i.multiple?Array.from(i.files):i.files[0])}),"showPicker"in HTMLInputElement.prototype?i.showPicker():i.click()})),Tn={__proto__:null,default:Cn},Pn=async(t=[{}])=>(Array.isArray(t)||(t=[t]),t[0].recursive=t[0].recursive||!1,new Promise((e,n)=>{const i=document.createElement("input");i.type="file",i.webkitdirectory=!0;const s=o=>{typeof a=="function"&&a(),e(o)},a=t[0].legacySetup&&t[0].legacySetup(s,()=>a(n),i);i.addEventListener("change",()=>{let o=Array.from(i.files);t[0].recursive?t[0].recursive&&t[0].skipDirectory&&(o=o.filter(r=>r.webkitRelativePath.split("/").every(c=>!t[0].skipDirectory({name:c,kind:"directory"})))):o=o.filter(r=>r.webkitRelativePath.split("/").length===2),s(o)}),"showPicker"in HTMLInputElement.prototype?i.showPicker():i.click()})),Rn={__proto__:null,default:Pn},An=async(t,e={})=>{Array.isArray(e)&&(e=e[0]);const n=document.createElement("a");let i=t;"body"in t&&(i=await async function(o,r){const c=o.getReader(),d=new ReadableStream({start:u=>async function v(){return c.read().then(({done:_,value:C})=>{if(!_)return u.enqueue(C),v();u.close()})}()}),h=new Response(d),l=await h.blob();return c.releaseLock(),new Blob([l],{type:r})}(t.body,t.headers.get("content-type"))),n.download=e.fileName||"Untitled",n.href=URL.createObjectURL(await i);const s=()=>{typeof a=="function"&&a()},a=e.legacySetup&&e.legacySetup(s,()=>a(),n);return n.addEventListener("click",()=>{setTimeout(()=>URL.revokeObjectURL(n.href),3e4),s()}),n.click(),null},In={__proto__:null,default:An};function On(t){return new Worker("/assets/svgoworker-DfXCDkWJ.js",{name:t?.name})}let F=null;const Ie=async t=>(F&&F.terminate(),F=new On,new Promise(e=>{const n=new MessageChannel;n.port1.onmessage=({data:i})=>{n.port1.close(),F&&(F.terminate(),F=null),e(i.result)},F.postMessage({svg:t},[n.port2])}));function De(t){return new Promise((e,n)=>{t.oncomplete=t.onsuccess=()=>e(t.result),t.onabort=t.onerror=()=>n(t.error)})}function Mn(t,e){const n=indexedDB.open(t);n.onupgradeneeded=()=>n.result.createObjectStore(e);const i=De(n);return(s,a)=>i.then(o=>a(o.transaction(e,s).objectStore(e)))}let Ue;function rt(){return Ue||(Ue=Mn("keyval-store","keyval")),Ue}function ne(t,e=rt()){return e("readonly",n=>De(n.get(t)))}function N(t,e,n=rt()){return n("readwrite",i=>(i.put(e,t),De(i.transaction)))}function $n(t,e=rt()){return e("readwrite",n=>(n.delete(t),De(n.transaction)))}const me="fileHandle",Vn=t=>t?t.name.replace(/\.[^\.]+$/,""):"";Ee.addEventListener("click",async()=>{try{const t=await vn({mimeTypes:["image/*"],description:"Image files"}),e=URL.createObjectURL(t);w.addEventListener("load",()=>{URL.revokeObjectURL(e)},{once:!0}),w.src=e,fe&&await N(me,t.handle)}catch(t){console.error(t.name,t.message),x(t.message)}});document.addEventListener("dragover",t=>{t.preventDefault()});document.addEventListener("dragenter",t=>{t.preventDefault(),R.classList.add("dropenter")});document.addEventListener("dragleave",t=>{t.preventDefault(),t.target===R&&R.classList.remove("dropenter")});document.addEventListener("drop",async t=>{t.preventDefault(),t.stopPropagation(),R.classList.remove("dropenter");const e=t.dataTransfer.items[0];if(e.kind==="file"){let n;if(w.addEventListener("load",()=>{URL.revokeObjectURL(n)},{once:!0}),fe){const s=await e.getAsFileSystemHandle();if(s.kind!=="file")return;const a=await s.getFile();n=URL.createObjectURL(a),w.src=n,await N(me,s);return}const i=e.getAsFile();n=URL.createObjectURL(i),w.src=n}});ke.addEventListener("click",async()=>{const t=async()=>{x(m.t("optimizingSVG"),1/0);const e=await Ie(y.innerHTML);return li(),new Blob([e],{type:"image/svg+xml"})};try{let e=!1;try{e=await ne(me)}catch{}const n=Vn(e);await yn(t(),{fileName:n,description:"SVG file",extensions:[".svg"],mimeTypes:["image/svg+xml"]}),x(m.t("savedSVG"))}catch(e){console.error(e.name,e.message),x(e.message)}});Ce.addEventListener("click",async()=>{try{const t=await navigator.clipboard.read();for(const e of t)for(const n of e.types)if(n.startsWith("image/")){const i=await e.getType(n);if(!i)return;const s=URL.createObjectURL(i);w.src=s;return}}catch(t){console.error(t.name,t.message),x(t.message)}});document.addEventListener("paste",t=>{try{if(!t.clipboardData.files.length)return;const e=t.clipboardData.files[0];if(e.type.startsWith("image/")){const n=URL.createObjectURL(e);w.src=n;return}}catch(e){console.error(e.name,e.message),x(e.message)}});Se.addEventListener("click",async()=>{let t=y.innerHTML;x(m.t("optimizingSVG"),1/0);try{"ClipboardItem"in window?"supports"in ClipboardItem&&ClipboardItem.supports("image/svg+xml")||!tn?(t=await Ie(t),await navigator.clipboard.write([new ClipboardItem({"image/svg+xml":new Promise(async e=>{e(new Blob([t],{type:"image/svg+xml"}))}),"text/plain":new Promise(async e=>{e(new Blob([t],{type:"text/plain"}))})})])):await navigator.clipboard.write([new ClipboardItem({"text/plain":new Blob([t],{type:"text/plain"})})]):await navigator.clipboard.writeText(await Ie(t))}catch{t=await Ie(t);const n=new Blob([t],{type:"text/plain"}),i=new Blob([t],{type:"image/svg+xml"});try{Number(navigator.userAgent.replace(/.*Chrome\/(\d+).*/,"$1"))<=88?await navigator.clipboard.write([new ClipboardItem({[n.type]:n})]):await navigator.clipboard.write([new ClipboardItem({[i.type]:i,[n.type]:n})])}catch{try{await navigator.clipboard.write([new ClipboardItem({[n.type]:n})])}catch(a){x(a.message);return}}}x(m.t("copiedSVG"))});class B{constructor(e){this.id=-1,this.nativePointer=e,this.pageX=e.pageX,this.pageY=e.pageY,this.clientX=e.clientX,this.clientY=e.clientY,self.Touch&&e instanceof Touch?this.id=e.identifier:le(e)&&(this.id=e.pointerId)}getCoalesced(){if("getCoalescedEvents"in this.nativePointer){const e=this.nativePointer.getCoalescedEvents().map(n=>new B(n));if(e.length>0)return e}return[this]}}const le=t=>"pointerId"in t,qe=t=>"changedTouches"in t,mt=()=>{};class zn{constructor(e,{start:n=()=>!0,move:i=mt,end:s=mt,rawUpdates:a=!1,avoidPointerEvents:o=!1}={}){this._element=e,this.startPointers=[],this.currentPointers=[],this._excludeFromButtonsCheck=new Set,this._pointerStart=r=>{if(le(r)&&r.buttons===0)this._excludeFromButtonsCheck.add(r.pointerId);else if(!(r.buttons&1))return;const c=new B(r);this.currentPointers.some(d=>d.id===c.id)||this._triggerPointerStart(c,r)&&(le(r)?((r.target&&"setPointerCapture"in r.target?r.target:this._element).setPointerCapture(r.pointerId),this._element.addEventListener(this._rawUpdates?"pointerrawupdate":"pointermove",this._move),this._element.addEventListener("pointerup",this._pointerEnd),this._element.addEventListener("pointercancel",this._pointerEnd)):(window.addEventListener("mousemove",this._move),window.addEventListener("mouseup",this._pointerEnd)))},this._touchStart=r=>{for(const c of Array.from(r.changedTouches))this._triggerPointerStart(new B(c),r)},this._move=r=>{if(!qe(r)&&(!le(r)||!this._excludeFromButtonsCheck.has(r.pointerId))&&r.buttons===0){this._pointerEnd(r);return}const c=this.currentPointers.slice(),d=qe(r)?Array.from(r.changedTouches).map(l=>new B(l)):[new B(r)],h=[];for(const l of d){const u=this.currentPointers.findIndex(v=>v.id===l.id);u!==-1&&(h.push(l),this.currentPointers[u]=l)}h.length!==0&&this._moveCallback(c,h,r)},this._triggerPointerEnd=(r,c)=>{if(!qe(c)&&c.buttons&1)return!1;const d=this.currentPointers.findIndex(l=>l.id===r.id);if(d===-1)return!1;this.currentPointers.splice(d,1),this.startPointers.splice(d,1),this._excludeFromButtonsCheck.delete(r.id);const h=!(c.type==="mouseup"||c.type==="touchend"||c.type==="pointerup");return this._endCallback(r,c,h),!0},this._pointerEnd=r=>{if(this._triggerPointerEnd(new B(r),r))if(le(r)){if(this.currentPointers.length)return;this._element.removeEventListener(this._rawUpdates?"pointerrawupdate":"pointermove",this._move),this._element.removeEventListener("pointerup",this._pointerEnd),this._element.removeEventListener("pointercancel",this._pointerEnd)}else window.removeEventListener("mousemove",this._move),window.removeEventListener("mouseup",this._pointerEnd)},this._touchEnd=r=>{for(const c of Array.from(r.changedTouches))this._triggerPointerEnd(new B(c),r)},this._startCallback=n,this._moveCallback=i,this._endCallback=s,this._rawUpdates=a&&"onpointerrawupdate"in window,self.PointerEvent&&!o?this._element.addEventListener("pointerdown",this._pointerStart):(this._element.addEventListener("mousedown",this._pointerStart),this._element.addEventListener("touchstart",this._touchStart),this._element.addEventListener("touchmove",this._move),this._element.addEventListener("touchend",this._touchEnd),this._element.addEventListener("touchcancel",this._touchEnd))}stop(){this._element.removeEventListener("pointerdown",this._pointerStart),this._element.removeEventListener("mousedown",this._pointerStart),this._element.removeEventListener("touchstart",this._touchStart),this._element.removeEventListener("touchmove",this._move),this._element.removeEventListener("touchend",this._touchEnd),this._element.removeEventListener("touchcancel",this._touchEnd),this._element.removeEventListener(this._rawUpdates?"pointerrawupdate":"pointermove",this._move),this._element.removeEventListener("pointerup",this._pointerEnd),this._element.removeEventListener("pointercancel",this._pointerEnd),window.removeEventListener("mousemove",this._move),window.removeEventListener("mouseup",this._pointerEnd)}_triggerPointerStart(e,n){return this._startCallback(e,n)?(this.currentPointers.push(e),this.startPointers.push(e),!0):!1}}function Dn(t,e){e===void 0&&(e={});var n=e.insertAt;if(!(!t||typeof document>"u")){var i=document.head||document.getElementsByTagName("head")[0],s=document.createElement("style");s.type="text/css",n==="top"&&i.firstChild?i.insertBefore(s,i.firstChild):i.appendChild(s),s.styleSheet?s.styleSheet.cssText=t:s.appendChild(document.createTextNode(t))}}var jn=`pinch-zoom {
  display: block;
  overflow: hidden;
  touch-action: none;
  --scale: 1;
  --x: 0;
  --y: 0;
}

pinch-zoom > * {
  transform: translate(var(--x), var(--y)) scale(var(--scale));
  transform-origin: 0 0;
  will-change: transform;
}
`;Dn(jn);const _e="min-scale";function gt(t,e){return e?Math.sqrt((e.clientX-t.clientX)**2+(e.clientY-t.clientY)**2):0}function ft(t,e){return e?{clientX:(t.clientX+e.clientX)/2,clientY:(t.clientY+e.clientY)/2}:t}function vt(t,e){return typeof t=="number"?t:t.trimRight().endsWith("%")?e*parseFloat(t)/100:parseFloat(t)}let wt;function Ot(){return wt||(wt=document.createElementNS("http://www.w3.org/2000/svg","svg"))}function We(){return Ot().createSVGMatrix()}function yt(){return Ot().createSVGPoint()}const Fe=.01;class Bn extends HTMLElement{constructor(){super(),this._transform=We(),new MutationObserver(()=>this._stageElChange()).observe(this,{childList:!0});const e=new zn(this,{start:(n,i)=>e.currentPointers.length===2||!this._positioningEl?!1:(i.preventDefault(),!0),move:n=>{this._onPointerMove(n,e.currentPointers)}});this.addEventListener("wheel",n=>this._onWheel(n))}static get observedAttributes(){return[_e]}attributeChangedCallback(e,n,i){e===_e&&this.scale<this.minScale&&this.setTransform({scale:this.minScale})}get minScale(){const e=this.getAttribute(_e);if(!e)return Fe;const n=parseFloat(e);return Number.isFinite(n)?Math.max(Fe,n):Fe}set minScale(e){this.setAttribute(_e,String(e))}connectedCallback(){this._stageElChange()}get x(){return this._transform.e}get y(){return this._transform.f}get scale(){return this._transform.a}scaleTo(e,n={}){let{originX:i=0,originY:s=0}=n;const{relativeTo:a="content",allowChangeEvent:o=!1}=n,r=a==="content"?this._positioningEl:this;if(!r||!this._positioningEl){this.setTransform({scale:e,allowChangeEvent:o});return}const c=r.getBoundingClientRect();if(i=vt(i,c.width),s=vt(s,c.height),a==="content")i+=this.x,s+=this.y;else{const d=this._positioningEl.getBoundingClientRect();i-=d.left,s-=d.top}this._applyChange({allowChangeEvent:o,originX:i,originY:s,scaleDiff:e/this.scale})}setTransform(e={}){const{scale:n=this.scale,allowChangeEvent:i=!1}=e;let{x:s=this.x,y:a=this.y}=e;if(!this._positioningEl){this._updateTransform(n,s,a,i);return}const o=this.getBoundingClientRect(),r=this._positioningEl.getBoundingClientRect();if(!o.width||!o.height){this._updateTransform(n,s,a,i);return}let c=yt();c.x=r.left-o.left,c.y=r.top-o.top;let d=yt();d.x=r.width+c.x,d.y=r.height+c.y;const h=We().translate(s,a).scale(n).multiply(this._transform.inverse());c=c.matrixTransform(h),d=d.matrixTransform(h),c.x>o.width?s+=o.width-c.x:d.x<0&&(s+=-d.x),c.y>o.height?a+=o.height-c.y:d.y<0&&(a+=-d.y),this._updateTransform(n,s,a,i)}_updateTransform(e,n,i,s){if(!(e<this.minScale)&&!(e===this.scale&&n===this.x&&i===this.y)&&(this._transform.e=n,this._transform.f=i,this._transform.d=this._transform.a=e,this.style.setProperty("--x",this.x+"px"),this.style.setProperty("--y",this.y+"px"),this.style.setProperty("--scale",this.scale+""),s)){const a=new Event("change",{bubbles:!0});this.dispatchEvent(a)}}_stageElChange(){this._positioningEl=void 0,this.children.length!==0&&(this._positioningEl=this.children[0],this.children.length>1&&console.warn("<pinch-zoom> must not have more than one child."),this.setTransform({allowChangeEvent:!0}))}_onWheel(e){if(!this._positioningEl)return;e.preventDefault();const n=this._positioningEl.getBoundingClientRect();let{deltaY:i}=e;const{ctrlKey:s,deltaMode:a}=e;a===1&&(i*=15);const r=1-i/(s?100:300);this._applyChange({scaleDiff:r,originX:e.clientX-n.left,originY:e.clientY-n.top,allowChangeEvent:!0})}_onPointerMove(e,n){if(!this._positioningEl)return;const i=this._positioningEl.getBoundingClientRect(),s=ft(e[0],e[1]),a=ft(n[0],n[1]),o=s.clientX-i.left,r=s.clientY-i.top,c=gt(e[0],e[1]),d=gt(n[0],n[1]),h=c?d/c:1;this._applyChange({originX:o,originY:r,scaleDiff:h,panX:a.clientX-s.clientX,panY:a.clientY-s.clientY,allowChangeEvent:!0})}_applyChange(e={}){const{panX:n=0,panY:i=0,originX:s=0,originY:a=0,scaleDiff:o=1,allowChangeEvent:r=!1}=e,c=We().translate(n,i).translate(s,a).translate(this.x,this.y).scale(o).translate(-s,-a).scale(this.scale);this.setTransform({allowChangeEvent:r,scale:c.a,x:c.e,y:c.f})}}customElements.define("pinch-zoom",Bn);const Hn='<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0z" fill="none"/><path d="M7 14c-1.66 0-3 1.34-3 3 0 1.31-1.16 2-2 2 .92 1.22 2.49 2 4 2 2.21 0 4-1.79 4-4 0-1.66-1.34-3-3-3zm13.71-9.37l-1.34-1.34c-.39-.39-1.02-.39-1.41 0L9 12.25 11.75 15l8.96-8.96c.39-.39.39-1.02 0-1.41z"/></svg>',Nn='<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="24" viewBox="0 0 24 24" width="24"><g><rect fill="none" height="24" width="24"/></g><g><g><path d="M17.66,17.66l-1.06,1.06l-0.71-0.71l1.06-1.06l-1.94-1.94l-1.06,1.06l-0.71-0.71l1.06-1.06l-1.94-1.94l-1.06,1.06 l-0.71-0.71l1.06-1.06L9.7,9.7l-1.06,1.06l-0.71-0.71l1.06-1.06L7.05,7.05L5.99,8.11L5.28,7.4l1.06-1.06L4,4v14c0,1.1,0.9,2,2,2 h14L17.66,17.66z M7,17v-5.76L12.76,17H7z"/></g></g></svg>',Un='<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0z" fill="none"/><path d="M15.96 10.29l-2.75 3.54-1.96-2.36L8.5 15h11l-3.54-4.71zM3 5H1v16c0 1.1.9 2 2 2h16v-2H3V5zm18-4H7c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2zm0 16H7V3h14v14z"/></svg>',qn='<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0z" fill="none"/><path d="M3 17v2h6v-2H3zM3 5v2h10V5H3zm10 16v-2h8v-2h-8v-2h-2v6h2zM7 9v2H3v2h4v2h2V9H7zm14 4v-2H11v2h10zm-6-4h2V7h4V5h-4V3h-2v6z"/></svg>',Wn='<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0z" fill="none"/><path d="M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V8h16v10z"/></svg>',Fn='<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0z" fill="none"/><path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/></svg>',Gn='<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0z" fill="none"/><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/></svg>',Yn='<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0z" fill="none"/><path d="M19 2h-4.18C14.4.84 13.3 0 12 0c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm7 18H5V4h2v3h10V4h2v16z"/></svg>',Xn='<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M16 5l-1.42 1.42-1.59-1.59V16h-1.98V4.83L9.42 6.42 8 5l4-4 4 4zm4 5v11c0 1.1-.9 2-2 2H6c-1.11 0-2-.9-2-2V10c0-1.11.89-2 2-2h3v2H6v11h12V10h-3V8h3c1.1 0 2 .89 2 2z"/></svg>',Kn='<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0z" fill="none"/><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/></svg>',Jn='<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0z" fill="none"/><path d="M3 17v2h6v-2H3zM3 5v2h10V5H3zm10 16v-2h8v-2h-8v-2h-2v6h2zM7 9v2H3v2h4v2h2V9H7zm14 4v-2H11v2h10zm-6-4h2V7h4V5h-4V3h-2v6z"/></svg>',Zn='<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="24" viewBox="0 0 24 24" width="24"><g><rect fill="none" height="24" width="24"/></g><g><g><path d="M20,17H4V5h8V3H4C2.89,3,2,3.89,2,5v12c0,1.1,0.89,2,2,2h4v2h8v-2h4c1.1,0,2-0.9,2-2v-3h-2V17z"/><polygon points="17,14 22,9 20.59,7.59 18,10.17 18,3 16,3 16,10.17 13.41,7.59 12,9"/></g></g></svg>',st="monochromeSettings",ot="colorSettings",Me="color",$e="monochrome",D="%",Mt="deg",de="steps",_t="pixels",nt="",Qn="segments",z={brightness:"brightness",contrast:"contrast",grayscale:"grayscale",hueRotate:"hue-rotate",invert:"invert",opacity:"opacity",saturate:"saturate",sepia:"sepia"},L={red:"red",green:"green",blue:"blue",alpha:"alpha"},ge={scale:"scale",rotation:"rotation"},k={minPathLength:"minPathSegments",strokeWidth:"strokeWidth",turdsize:"turdsize",alphamax:"alphamax",turnpolicy:"turnpolicy",opticurve:"opticurve",opttolerance:"opttolerance"},$t={[z.brightness]:{unit:D,initial:100,min:0,max:200},[z.contrast]:{unit:D,initial:100,min:0,max:200},[z.grayscale]:{unit:D,initial:0,min:0,max:100},[z.hueRotate]:{unit:Mt,initial:0,min:0,max:360},[z.invert]:{unit:D,initial:0,min:0,max:100},[z.opacity]:{unit:D,initial:100,min:0,max:100},[z.saturate]:{unit:D,initial:100,min:0,max:200},[z.sepia]:{unit:D,initial:0,min:0,max:100}},ei={[L.red]:{unit:de,initial:5,min:1,max:20},[L.green]:{unit:de,initial:5,min:1,max:20},[L.blue]:{unit:de,initial:5,min:1,max:20},[L.alpha]:{unit:de,initial:1,min:1,max:10}},ti={[ge.scale]:{unit:D,initial:100,min:1,max:200},[ge.rotation]:{unit:Mt,initial:0,min:0,max:360}},ni={[k.turdsize]:{unit:_t,initial:2,min:0,max:50},[k.strokeWidth]:{unit:_t,initial:0,min:0,max:100},[k.minPathLength]:{unit:Qn,initial:0,min:0,max:30},[k.alphamax]:{unit:nt,initial:1,min:0,max:1.3334},[k.turnpolicy]:{unit:de,initial:4,min:0,max:6},[k.opttolerance]:{unit:nt,initial:.2,min:0,max:1}},ii=[{name:"svgOptions",icon:qn},{name:"colorChannels",icon:Hn},{name:"imageSizeAndRotation",icon:Nn},{name:"imagePreprocessing",icon:Un}],at=[Object.entries(ni),Object.entries(ei),Object.entries(ti),Object.entries($t)],g={},Ve={},M={},ie=(t,e)=>{const n=m.t(t);return` (${t?`${e}${n.length===1?n:` ${n}`}`:e})`},j=t=>{const e=document.createElement("span");return e.classList.add("icon"),e.innerHTML=t,e},ri=(t,e)=>{const n=document.createElement("details");M[t]=n;const i=document.createElement("summary"),s=j(e);i.append(s);const a=document.createElement("span");return a.textContent=m.t(t),a.dataset.i18nKey=t,i.append(a),n.append(i),n},si=["alphamax","turnpolicy","optimize-curves","opttolerance","minPathSegments"],oi=async(t,e,n)=>{const{unit:i,min:s,max:a,initial:o}=e,r=document.createElement("div");r.classList.add("preprocess-input"),si.includes(t)&&r.classList.add("advanced");const c=document.createElement("label"),d=document.createElement("span");d.textContent=m.t(t),d.dataset.i18nKey=t,c.append(d),c.htmlFor=t;const h=await je(),l=document.createElement("span");Ve[t]=l,l.textContent=ie(i,h[t]||o),l.dataset.dynamicI18nKey=i,l.dataset.dynamicValue=h[t]||o;const u=document.createElement("input");g[t]=u,u.id=t,u.type="range",u.class=t,i&&(u.dataset.unit=i),i===nt&&(u.step=.01),u.min=s,u.max=a,u.value=h[t]||o,u.addEventListener("input",()=>{l.textContent=ie(i,u.value),l.dataset.dynamicValue=u.value}),Object.keys(L).includes(t)?u.addEventListener("change",Re(async()=>{await Y(u),await O()},250)):Object.keys(k).includes(t)?u.addEventListener("change",Re(async()=>{await Y(u),await O()},250)):u.addEventListener("change",Re(async()=>{await Y(u),await O()},250));const v=document.createElement("button");v.type="button",v.textContent=m.t("reset"),v.dataset.i18nKey="reset",v.addEventListener("click",async()=>{u.value=o,l.textContent=ie(i,o),l.dataset.dynamicValue=o,u.dispatchEvent(new Event("change"))}),c.append(l),r.append(c);const _=document.createElement("div");r.append(_),_.append(u),_.append(v),n.append(r)},Vt=()=>{const t=!A.checked;Object.keys(L).forEach(e=>{g[e].disabled=t})};A.addEventListener("change",async()=>{Vt(),await Y(A),await O()});const ct=async()=>{const t=await je();A.checked=t[A.id]??A.defaultChecked,Vt(),V.checked=t[V.id]??V.defaultChecked,P.checked=t[P.id]??P.defaultChecked,zt(),G.checked=t[G.id]??G.defaultChecked,jt(),at.forEach(e=>{for(const[n,i]of e){const s=t[g[n].id]||i.initial;g[n].value=s,Ve[n].textContent=ie(i.unit,s)}})};E.addEventListener("change",async()=>{await N(E.id,E.checked),await N(T.id,T.checked),await ct(),await O()});T.addEventListener("change",async()=>{await N(E.id,E.checked),await N(T.id,T.checked),await ct(),await O()});V.addEventListener("change",async()=>{await Y(V),await O()});const zt=()=>{g.opttolerance.disabled=!P.checked};P.addEventListener("change",async()=>{zt(),await Y(P),await O()});ue.addEventListener("input",()=>{I.classList.toggle("debug",ue.checked)});const ai=async()=>{await m.getTranslations(),Dt();const t=getComputedStyle(R).getPropertyValue("--mobile-breakpoint"),e=window.matchMedia(`(max-width: ${t})`),n=()=>{if(e.matches){tt.open=!1;return}tt.open=!0};n(),e.addEventListener("change",n);try{E.checked=await ne(E.id)??E.defaultChecked}catch{E.checked=E.defaultChecked}try{T.checked=await ne(T.id)??T.defaultChecked}catch{T.checked=T.defaultChecked}E.checked&&y.classList.add(Me),T.checked&&y.classList.add($e);const i=[];at.forEach(async(o,r)=>{const{name:c,icon:d}=ii[r],h=ri(c,d);dt.append(h),r<2&&(h.open=!0),c==="colorChannels"?M.colorChannels.append(A.parentNode):c==="svgOptions"?(M.svgOptions.append(E.parentNode),M.svgOptions.append(T.parentNode)):c==="imageSizeAndRotation"&&M.imageSizeAndRotation.append(V.parentNode);for(const[l,u]of o)i.push(oi(l,u,h));Promise.all(i).then(async()=>{for(const[l]of o){if(l==="opttolerance"){const u=M.svgOptions.querySelector('[for="opttolerance"]').parentNode;M.svgOptions.insertBefore(P.parentNode,u)}if(c==="svgOptions"){const u=M.svgOptions.querySelector(".preprocess-input.advanced");M.svgOptions.insertBefore(G.parentNode,u)}}await ct()})}),dt.append(it.parentNode),w.addEventListener("load",async()=>{w.width=w.naturalWidth,w.height=w.naturalHeight;const o=await je();if(w.src!==new URL("/favicon.png",location.href).toString()||Object.keys(o).length>1)setTimeout(async()=>{Ct(),await O()},100);else{const r=await fetch(`/potraced-${E.checked?"color":"monochrome"}.svg`).then(c=>c.text());y.innerHTML=r}}),w.complete&&w.dispatchEvent(new Event("load")),Pe.hidden=!1,new URL(location).searchParams.has("debug")&&(ue.checked=!0,ue.labels[0].hidden=!1,ue.dispatchEvent(new Event("input")));try{const o=await ne(me);if(o&&await ci(o)){const r=await o.getFile(),c=URL.createObjectURL(r);w.src=c}}catch(o){console.error(o.name,o.message);try{await $n(me)}catch(r){console.error(r.name,r.message)}}},ci=async t=>{const e={mode:"read"};return await t.queryPermission(e)==="granted"||await t.requestPermission(e)==="granted"},Dt=()=>{Jt.textContent=m.t("license"),Zt.textContent=m.t("about"),it.textContent=m.t("resetAll"),Wt.textContent=m.t("posterizeInputImage"),Ft.textContent=m.t("colorSVG"),Gt.textContent=m.t("monochromeSVG"),Yt.textContent=m.t("considerDPR"),Xt.textContent=m.t("opticurve"),Kt.textContent=m.t("showAdvancedControls"),Ee.innerHTML="",Ee.append(j(Wn));const t=document.createElement("span");t.textContent=m.t("openImage"),Ee.append(t),ke.innerHTML="",ke.append(j(Fn));const e=document.createElement("span");e.textContent=m.t("saveSVG"),ke.append(e),xe.innerHTML="",xe.append(j(en?Xn:Kn));const n=document.createElement("span");n.textContent=m.t("shareSVG"),xe.append(n),Se.innerHTML="",Se.append(j(Gn));const i=document.createElement("span");i.textContent=m.t("copySVG"),Se.append(i),Ce.innerHTML="",Ce.append(j(Yn));const s=document.createElement("span");s.textContent=m.t("pasteImage"),Ce.append(s),Te.innerHTML="",Te.append(j(Zn));const a=document.createElement("span");a.textContent=m.t("install"),Te.append(a),R.dataset.dropText=m.t("dropFileHere"),Be.innerHTML="",Be.append(j(Jn)),Be.append(document.createTextNode(m.t("tweak"))),St.ariaLabel=m.t("closeOptions"),document.querySelectorAll("[data-i18n-key]").forEach(o=>{o.textContent=m.t(o.dataset.i18nKey)}),document.querySelectorAll("[data-dynamic-i18n-key]").forEach(o=>{o.textContent=ie(o.dataset.dynamicI18nKey,o.dataset.dynamicValue)}),Oe.innerHTML="",m.supportedLocales.sort().forEach(o=>{const[r,c]=o.split("-"),d=document.createElement("option");d.value=o,d.textContent=m.t(`${r}${c}`),r===m.currentLanguageAndLocale.language&&c===m.currentLanguageAndLocale.locale&&(d.selected=!0),Oe.append(d)})};Oe.addEventListener("change",async()=>{const[t,e]=Oe.value.split("-");try{await m.setLanguageAndLocale(t,e),Dt()}catch(n){console.error(n.name,n.message)}});it.addEventListener("click",async()=>{const t=(e,n,i)=>{g[e].value=i,Ve[e].textContent=ie(n,i),Ve[e].dataset.dynamicValue=i};at.forEach(e=>{for(const[n,i]of e)t(n,i.unit,i.initial)}),P.checked=P.defaultChecked,opttolerance.disabled=!P.defaultChecked,A.checked=A.defaultChecked,V.checked=V.defaultChecked,await di(),Ct(),await O()});let Ge=null;const x=(t,e=5e3)=>{if(Z.innerHTML=t,Z.hidden=!1,Ge&&clearTimeout(Ge),e!==1/0){Ge=setTimeout(()=>{Z.hidden=!0,Z.textContent=""},e);return}},li=()=>{Z.hidden=!0,Z.textContent=""},jt=async()=>{await Y(G),document.querySelectorAll(".advanced").forEach(t=>{G.checked?t.style.display="block":t.style.display="none"})};G.addEventListener("change",jt);R.style.setProperty("--100vh",`${window.innerHeight}px`);window.addEventListener("resize",Re(()=>{R.style.setProperty("--100vh",`${window.innerHeight}px`)},250));St.addEventListener("click",()=>{tt.open=!1});const di=async()=>{await N(E.checked?ot:st,{})},je=async()=>{try{const t=E.checked?await ne(ot):await ne(st);return t||{}}catch{return{}}},Y=async t=>{try{const e=await je();e[t.id]=t.type==="range"?t.value:t.checked,await N(E.checked?ot:st,e)}catch{}};function hi(t={}){const{immediate:e=!1,onNeedRefresh:n,onOfflineReady:i,onRegistered:s,onRegisteredSW:a,onRegisterError:o}=t;let r,c;const d=async(l=!0)=>{await c};async function h(){if("serviceWorker"in navigator){if(r=await p(()=>import("./workbox-window.prod.es5-DFjpnwFp.js"),__vite__mapDeps([])).then(({Workbox:l})=>new l("/sw.js",{scope:"/",type:"classic"})).catch(l=>{o?.(l)}),!r)return;r.addEventListener("activated",l=>{(l.isUpdate||l.isExternal)&&window.location.reload()}),r.addEventListener("installed",l=>{l.isUpdate||i?.()}),r.register({immediate:e}).then(l=>{a?a("/sw.js",l):s?.(l)}).catch(l=>{o?.(l)})}}return c=h(),d}// @license © 2019 Google LLC. Licensed under the Apache License, Version 2.0.
const he=document;let J={};try{J=localStorage}catch{}const ze="prefers-color-scheme",be="media",b="light",S="dark",bt=`(${ze}:${S})`,ui=`(${ze}:${b})`,Lt="link[rel=stylesheet]",Ye="remember",Xe="legend",Le="toggle",Et="switch",Ke="appearance",Je="permanent",Ze="mode",ce="colorschemechange",Qe="permanentcolorscheme",kt="all",et="not all",f="dark-mode-toggle",te="https://googlechromelabs.github.io/dark-mode-toggle/demo/",K=(t,e,n=e)=>{Object.defineProperty(t,n,{enumerable:!0,get(){const i=this.getAttribute(e);return i===null?"":i},set(i){this.setAttribute(e,i)}})},pi=(t,e,n=e)=>{Object.defineProperty(t,n,{enumerable:!0,get(){return this.hasAttribute(e)},set(i){i?this.setAttribute(e,""):this.removeAttribute(e)}})},Bt=he.createElement("template");Bt.innerHTML=`<style>*,::after,::before{box-sizing:border-box}:host{contain:content;display:block}:host([hidden]){display:none}form{background-color:var(--${f}-background-color,transparent);color:var(--${f}-color,inherit);padding:0}fieldset{border:none;margin:0;padding-block:.25rem;padding-inline:.25rem}legend{font:var(--${f}-legend-font,inherit);padding:0}input,label{cursor:pointer}label{white-space:nowrap}input{opacity:0;position:absolute;pointer-events:none}input:focus-visible+label{outline:#e59700 auto 2px;outline:-webkit-focus-ring-color auto 5px}label:not(:empty)::before{margin-inline-end:.5rem;}label::before{content:"";display:inline-block;background-size:var(--${f}-icon-size,1rem);background-repeat:no-repeat;height:var(--${f}-icon-size,1rem);width:var(--${f}-icon-size,1rem);vertical-align:middle;}[part=lightLabel]::before{background-image:var(--${f}-light-icon, url("${te}sun.png"))}[part=darkLabel]::before{filter:var(--${f}-icon-filter, none);background-image:var(--${f}-dark-icon, url("${te}moon.png"))}[part=toggleLabel]::before{background-image:var(--${f}-checkbox-icon,none)}[part=permanentLabel]::before{background-image:var(--${f}-remember-icon-unchecked, url("${te}unchecked.svg"))}[part=darkLabel],[part=lightLabel],[part=toggleLabel]{font:var(--${f}-label-font,inherit)}[part=darkLabel]:empty,[part=lightLabel]:empty,[part=toggleLabel]:empty{font-size:0;padding:0}[part=permanentLabel]{font:var(--${f}-remember-font,inherit)}input:checked+[part=permanentLabel]::before{background-image:var(--${f}-remember-icon-checked, url("${te}checked.svg"))}input:checked+[part=darkLabel],input:checked+[part=lightLabel]{background-color:var(--${f}-active-mode-background-color,transparent)}input:checked+[part=darkLabel]::before,input:checked+[part=lightLabel]::before{background-color:var(--${f}-active-mode-background-color,transparent)}input:checked+[part=toggleLabel]::before{filter:var(--${f}-icon-filter, none)}input:checked+[part=toggleLabel]+aside [part=permanentLabel]::before{filter:var(--${f}-remember-filter, invert(100%))}aside{visibility:hidden;margin-block-start:.15rem}[part=darkLabel]:focus-visible~aside,[part=lightLabel]:focus-visible~aside,[part=toggleLabel]:focus-visible~aside{visibility:visible;transition:visibility 0s}aside [part=permanentLabel]:empty{display:none}@media (hover:hover){aside{transition:visibility 3s}aside:hover{visibility:visible}[part=darkLabel]:hover~aside,[part=lightLabel]:hover~aside,[part=toggleLabel]:hover~aside{visibility:visible;transition:visibility 0s}}</style><form part=form><fieldset part=fieldset><legend part=legend></legend><input part=lightRadio id=l name=mode type=radio><label part=lightLabel for=l></label><input part=darkRadio id=d name=mode type=radio><label part=darkLabel for=d></label><input part=toggleCheckbox id=t type=checkbox><label part=toggleLabel for=t></label><aside part=aside><input part=permanentCheckbox id=p type=checkbox><label part=permanentLabel for=p></label></aside></fieldset></form>`;class mi extends HTMLElement{static get observedAttributes(){return[Ze,Ke,Je,Xe,b,S,Ye]}constructor(){super(),K(this,Ze),K(this,Ke),K(this,Xe),K(this,b),K(this,S),K(this,Ye),pi(this,Je),this.t=null,this.i=null,he.addEventListener(ce,e=>{this.mode=e.detail.colorScheme,this.o(),this.l()}),he.addEventListener(Qe,e=>{this.permanent=e.detail.permanent,this.h.checked=this.permanent}),this.p()}p(){const e=this.attachShadow({mode:"open"});e.append(Bt.content.cloneNode(!0)),this.t=he.querySelectorAll(`${Lt}[${be}*=${ze}][${be}*="${S}"]`),this.i=he.querySelectorAll(`${Lt}[${be}*=${ze}][${be}*="${b}"]`),this.m=e.querySelector("[part=lightRadio]"),this.u=e.querySelector("[part=lightLabel]"),this.k=e.querySelector("[part=darkRadio]"),this.v=e.querySelector("[part=darkLabel]"),this.$=e.querySelector("[part=toggleCheckbox]"),this.L=e.querySelector("[part=toggleLabel]"),this.C=e.querySelector("legend"),this.M=e.querySelector("aside"),this.h=e.querySelector("[part=permanentCheckbox]"),this.R=e.querySelector("[part=permanentLabel]")}connectedCallback(){const e=matchMedia(bt).media!==et;e&&matchMedia(bt).addListener(({matches:i})=>{this.permanent||(this.mode=i?S:b,this._(ce,{colorScheme:this.mode}))});let n=!1;try{n=J.getItem(f)}catch{}if(n&&[S,b].includes(n)?(this.mode=n,this.h.checked=!0,this.permanent=!0):e&&(this.mode=matchMedia(ui).matches?b:S),this.mode||(this.mode=b),this.permanent&&!n)try{J.setItem(f,this.mode)}catch{}this.appearance||(this.appearance=Le),this.A(),this.o(),this.l(),[this.m,this.k].forEach(i=>{i.addEventListener("change",()=>{this.mode=this.m.checked?b:S,this.l(),this._(ce,{colorScheme:this.mode})})}),this.$.addEventListener("change",()=>{this.mode=this.$.checked?S:b,this.o(),this._(ce,{colorScheme:this.mode})}),this.h.addEventListener("change",()=>{this.permanent=this.h.checked,this._(Qe,{permanent:this.permanent})}),this.S(),this._(ce,{colorScheme:this.mode}),this._(Qe,{permanent:this.permanent})}attributeChangedCallback(e,n,i){if(e===Ze){if(![b,S].includes(i))throw new RangeError(`Allowed values: "${b}" and "${S}".`);if(matchMedia("(hover:none)").matches&&this.remember&&this.T(),this.permanent)try{J.setItem(f,this.mode)}catch{}this.o(),this.l(),this.S()}else if(e===Ke){if(![Le,Et].includes(i))throw new RangeError(`Allowed values: "${Le}" and "${Et}".`);this.A()}else if(e===Je){if(this.permanent){if(this.mode)try{J.setItem(f,this.mode)}catch{}}else try{J.removeItem(f)}catch{}this.h.checked=this.permanent}else e===Xe?this.C.textContent=i:e===Ye?this.R.textContent=i:e===b?(this.u.textContent=i,this.mode===b&&(this.L.textContent=i)):e===S&&(this.v.textContent=i,this.mode===S&&(this.L.textContent=i))}_(e,n){this.dispatchEvent(new CustomEvent(e,{bubbles:!0,composed:!0,detail:n}))}A(){const e=this.appearance===Le;this.m.hidden=e,this.u.hidden=e,this.k.hidden=e,this.v.hidden=e,this.$.hidden=!e,this.L.hidden=!e}o(){this.mode===b?this.m.checked=!0:this.k.checked=!0}l(){this.mode===b?(this.L.style.setProperty(`--${f}-checkbox-icon`,`var(--${f}-light-icon,url("${te}moon.png"))`),this.L.textContent=this.light,this.light||(this.L.ariaLabel=S),this.$.checked=!1):(this.L.style.setProperty(`--${f}-checkbox-icon`,`var(--${f}-dark-icon,url("${te}sun.png"))`),this.L.textContent=this.dark,this.dark||(this.L.ariaLabel=b),this.$.checked=!0)}S(){this.mode===b?(this.i.forEach(e=>{e.media=kt,e.disabled=!1}),this.t.forEach(e=>{e.media=et,e.disabled=!0})):(this.t.forEach(e=>{e.media=kt,e.disabled=!1}),this.i.forEach(e=>{e.media=et,e.disabled=!0}))}T(){this.M.style.visibility="visible",setTimeout(()=>{this.M.style.visibility="hidden"},3e3)}}customElements.define(f,mi);"launchQueue"in window&&p(()=>import("./filehandling-ppwoROA8.js"),__vite__mapDeps([]));"windowControlsOverlay"in navigator&&p(()=>import("./windowcontrols-B6bju7oe.js"),__vite__mapDeps([]));"onbeforeinstallprompt"in window&&"onappinstalled"in window?p(()=>import("./install-DX-WpHQu.js"),__vite__mapDeps([])):Te.style.display="none";"share"in navigator&&"canShare"in navigator?p(()=>import("./share-BJnRZ6E5.js"),__vite__mapDeps([])):xe.style.display="none";"serviceWorker"in navigator&&window.addEventListener("load",async()=>{try{await navigator.serviceWorker.register("./sharetargetsw.js",{scope:"/share-target/"})}catch(t){console.error(t.name,t.message),x(t.message)}if(location.search.includes("share-target")){const t=await caches.keys(),e=await caches.open(t.filter(i=>i.startsWith("media"))[0]),n=await e.match("shared-image");if(n){const i=await n.blob();await e.delete("shared-image");const s=URL.createObjectURL(i);w.addEventListener("load",()=>{URL.revokeObjectURL(s)},{once:!0}),w.src=s}}});const gi=()=>{let t=!1;const e={get type(){t=!0}};try{new Worker("blob://",e)}finally{return t}},Ht=()=>{const t=xt.mode;R.style.setProperty("--color-scheme",t),Qt.content=t==="dark"?"#131313":"#fff"};xt.addEventListener("colorschemechange",Ht);Ht();(async()=>(ai(),gi()||await p(()=>import("./module-workers-polyfill.min-B4htaMKj.js"),__vite__mapDeps([])),hi({onOfflineReady(){x(m.t("readyToWorkOffline"))},onNeedRefresh(){location.reload()}})()))();export{me as F,vi as a,Te as b,xe as c,Re as d,y as e,x as f,m as g,li as h,w as i,ne as j,Vn as k,fi as m,Ie as o,N as s};
