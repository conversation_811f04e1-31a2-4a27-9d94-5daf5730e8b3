import e from"./languages-BtQEISXM.js";const t={red:"Красный",green:"Зелёный",blue:"Синий",alpha:"Прозрачность",brightness:"Яркость",contrast:"Контраст",grayscale:"Монохром","hue-rotate":"Поворот цвета",invert:"Инверсия",opacity:"Прозрачность",saturate:"Насыщенность",sepia:"Сепия",scale:"Масштаб",rotation:"Поворот",turdsize:"Уменьшить мусор",alphamax:"Угловой порог",minPathSegments:"Минимальная длина пути",strokeWidth:"Толщина обводки",turnpolicy:"Правила поворота",opticurve:"Оптимизировать кривые",opttolerance:"Уровень оптимизации",showAdvancedControls:"Дополнительные настройки","%":"%",deg:"°",steps:"шагов",pixels:"пикселей",segments:"сегментов",reset:"Сбросить",resetAll:"Сбросить всё",dropFileHere:"Бросьте файл сюда",openImage:"Открыть картинку",saveSVG:"Сохранить SVG",pasteImage:"Вставить картинку",copySVG:"Скопировать SVG",shareSVG:"Поделиться SVG",install:"Установить",posterizeInputImage:"Постеризовать входную картинку",colorSVG:"Цветной SVG",monochromeSVG:"Монохромный SVG",colorChannels:"Цветовые каналы",imageSizeAndRotation:"Входные размеры и поворот",imagePreprocessing:"Входная обработка",svgOptions:"Настройки SVG",considerDPR:"Учитывать плотность пикселей",tweak:"Подкрутить",closeOptions:"Закрыть",optimizingSVG:"Оптимизирую SVG",copiedSVG:"Скопированный SVG",savedSVG:"Сохранённый SVG",readyToWorkOffline:"Готово для работы офлайн.",svgSize:"Размер SVG",zoom:"Масштаб",license:"Лицензия",about:"О проекте",...e};export{t as default};
