/**
 * SVGcode—Convert raster images to SVG vector graphics
 * Copyright (C) 2021 Google LLC
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 */

/**
 * Parse SVG path data into command objects
 * @param {string} pathData - SVG path data string
 * @returns {Array} Array of path command objects
 */
const parsePathData = (pathData) => {
  const commands = [];
  const regex = /([MmLlHhVvCcSsQqTtAaZz])((?:\s*[-+]?(?:\d+\.?\d*|\.\d+)(?:[eE][-+]?\d+)?\s*,?\s*)*)/g;
  let match;

  while ((match = regex.exec(pathData)) !== null) {
    const command = match[1];
    const params = match[2].trim();
    const values = params ? params.split(/[\s,]+/).map(Number).filter(n => !isNaN(n)) : [];
    
    commands.push({ command, values });
  }
  
  return commands;
};

/**
 * Convert command objects back to SVG path data string
 * @param {Array} commands - Array of path command objects
 * @returns {string} SVG path data string
 */
const commandsToPathData = (commands) => {
  return commands.map(cmd => {
    if (cmd.values.length === 0) {
      return cmd.command;
    }
    return cmd.command + cmd.values.join(',');
  }).join(' ');
};

/**
 * Douglas-Peucker path simplification algorithm
 * @param {Array} points - Array of {x, y} points
 * @param {number} tolerance - Simplification tolerance
 * @returns {Array} Simplified array of points
 */
const douglasPeucker = (points, tolerance = 1.0) => {
  if (points.length <= 2) return points;

  const distancePointToLine = (point, lineStart, lineEnd) => {
    const A = point.x - lineStart.x;
    const B = point.y - lineStart.y;
    const C = lineEnd.x - lineStart.x;
    const D = lineEnd.y - lineStart.y;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    
    if (lenSq === 0) return Math.sqrt(A * A + B * B);
    
    const param = dot / lenSq;
    let xx, yy;

    if (param < 0) {
      xx = lineStart.x;
      yy = lineStart.y;
    } else if (param > 1) {
      xx = lineEnd.x;
      yy = lineEnd.y;
    } else {
      xx = lineStart.x + param * C;
      yy = lineStart.y + param * D;
    }

    const dx = point.x - xx;
    const dy = point.y - yy;
    return Math.sqrt(dx * dx + dy * dy);
  };

  let maxDistance = 0;
  let maxIndex = 0;
  const end = points.length - 1;

  for (let i = 1; i < end; i++) {
    const distance = distancePointToLine(points[i], points[0], points[end]);
    if (distance > maxDistance) {
      maxDistance = distance;
      maxIndex = i;
    }
  }

  if (maxDistance > tolerance) {
    const left = douglasPeucker(points.slice(0, maxIndex + 1), tolerance);
    const right = douglasPeucker(points.slice(maxIndex), tolerance);
    return left.slice(0, -1).concat(right);
  }

  return [points[0], points[end]];
};

/**
 * Convert linear path segments to smooth cubic Bézier curves
 * @param {Array} points - Array of {x, y} points
 * @param {number} smoothness - Smoothness factor (0-1)
 * @returns {Array} Array of cubic Bézier curve commands
 */
const convertToSmoothCurves = (points, smoothness = 0.3) => {
  if (points.length < 3) return points;

  const curves = [];
  curves.push({ command: 'M', values: [points[0].x, points[0].y] });

  for (let i = 1; i < points.length - 1; i++) {
    const prev = points[i - 1];
    const curr = points[i];
    const next = points[i + 1];

    // Calculate control points for smooth curve
    const cp1x = curr.x - (next.x - prev.x) * smoothness;
    const cp1y = curr.y - (next.y - prev.y) * smoothness;
    const cp2x = curr.x + (next.x - prev.x) * smoothness;
    const cp2y = curr.y + (next.y - prev.y) * smoothness;

    if (i === 1) {
      // First curve: use quadratic
      curves.push({
        command: 'Q',
        values: [cp1x, cp1y, curr.x, curr.y]
      });
    } else {
      // Subsequent curves: use cubic
      curves.push({
        command: 'C',
        values: [cp1x, cp1y, cp2x, cp2y, curr.x, curr.y]
      });
    }
  }

  // Add final point
  const lastPoint = points[points.length - 1];
  curves.push({ command: 'L', values: [lastPoint.x, lastPoint.y] });

  return curves;
};

/**
 * Extract points from path commands
 * @param {Array} commands - Array of path command objects
 * @returns {Array} Array of {x, y} points
 */
const extractPoints = (commands) => {
  const points = [];
  let currentX = 0, currentY = 0;

  commands.forEach(cmd => {
    switch (cmd.command.toLowerCase()) {
      case 'm':
        if (cmd.command === 'M') {
          currentX = cmd.values[0];
          currentY = cmd.values[1];
        } else {
          currentX += cmd.values[0];
          currentY += cmd.values[1];
        }
        points.push({ x: currentX, y: currentY });
        break;
      case 'l':
        if (cmd.command === 'L') {
          currentX = cmd.values[0];
          currentY = cmd.values[1];
        } else {
          currentX += cmd.values[0];
          currentY += cmd.values[1];
        }
        points.push({ x: currentX, y: currentY });
        break;
      case 'h':
        if (cmd.command === 'H') {
          currentX = cmd.values[0];
        } else {
          currentX += cmd.values[0];
        }
        points.push({ x: currentX, y: currentY });
        break;
      case 'v':
        if (cmd.command === 'V') {
          currentY = cmd.values[0];
        } else {
          currentY += cmd.values[0];
        }
        points.push({ x: currentX, y: currentY });
        break;
      case 'c':
        if (cmd.command === 'C') {
          currentX = cmd.values[4];
          currentY = cmd.values[5];
        } else {
          currentX += cmd.values[4];
          currentY += cmd.values[5];
        }
        points.push({ x: currentX, y: currentY });
        break;
    }
  });

  return points;
};

/**
 * Apply curve smoothing to SVG path data
 * @param {string} pathData - Original SVG path data
 * @param {Object} options - Smoothing options
 * @returns {string} Smoothed SVG path data
 */
const smoothPath = (pathData, options = {}) => {
  const {
    simplificationTolerance = 1.0,
    curveSmoothness = 0.3,
    enableSimplification = true,
    enableCurveConversion = true
  } = options;

  try {
    const commands = parsePathData(pathData);
    let points = extractPoints(commands);

    if (points.length < 2) return pathData;

    // Apply Douglas-Peucker simplification
    if (enableSimplification && points.length > 2) {
      points = douglasPeucker(points, simplificationTolerance);
    }

    // Convert to smooth curves
    if (enableCurveConversion && points.length > 2) {
      const smoothCommands = convertToSmoothCurves(points, curveSmoothness);
      return commandsToPathData(smoothCommands);
    }

    return pathData;
  } catch (error) {
    console.warn('Error in path smoothing:', error);
    return pathData; // Return original on error
  }
};

/**
 * Apply curve smoothing to entire SVG
 * @param {string} svgString - Original SVG string
 * @param {Object} options - Smoothing options
 * @returns {string} SVG with smoothed paths
 */
const smoothSVG = (svgString, options = {}) => {
  try {
    // Extract and smooth all path elements
    return svgString.replace(/<path[^>]*d="([^"]*)"[^>]*>/g, (match, pathData) => {
      const smoothedPath = smoothPath(pathData, options);
      return match.replace(pathData, smoothedPath);
    });
  } catch (error) {
    console.warn('Error in SVG smoothing:', error);
    return svgString; // Return original on error
  }
};

export { smoothSVG, smoothPath };
