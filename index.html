<!doctype html>
<html dir="ltr" lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <meta
      name="description"
      content="Convert color or monochrome bitmap images (PNG, JPG, JPEG, WEBP, AVIF, GIF,…) to color or monochrome vector images (SVG)."
    />
    <meta name="theme-color" content="#131313" />
    <meta name="color-scheme" content="dark light" />
    <meta
      name="description"
      content="SVGcode is a Progressive Web App that lets you convert raster images like JPG, PNG, GIF, WebP, AVIF, etc. to vector graphics in SVG format."
    />

    <meta property="og:title" content="SVGcode" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://svgco.de/" />
    <meta
      property="og:image"
      content="https://svgco.de/screenshots/desktop-dark.png"
    />
    <meta
      property="og:description"
      content="SVGcode is a Progressive Web App that lets you convert raster images like JPG, PNG, GIF, WebP, AVIF, etc. to vector graphics in SVG format."
    />
    <meta name="monetization" content="$ilp.gatehub.net/348218105" />

    <!-- (Chrome) Web App Dark Mode -->
    <meta
      http-equiv="origin-trial"
      content="AhLXeDFYJ0yF4e0It5evBIxgcWJDxcRzIK7mRSwEdWTv0CwetrQZHJHKe5j3WLcf1lZYkzKJZtW+88rSA4yzZAYAAABQeyJvcmlnaW4iOiJodHRwczovL3N2Z2NvLmRlOjQ0MyIsImZlYXR1cmUiOiJXZWJBcHBEYXJrTW9kZSIsImV4cGlyeSI6MTY3NTgxNDM5OX0="
    />

    <!-- (Chrome) Web App Dark Mode v2 -->
    <meta
      http-equiv="origin-trial"
      content="AszAyNxfEENEzLjioa9hIxoDYUte+uiWWUhEZWXlvAZhSf4Tk7X5CjUinKSJ4xS56euPn8EChrxIB8A8orvt8g4AAABSeyJvcmlnaW4iOiJodHRwczovL3N2Z2NvLmRlOjQ0MyIsImZlYXR1cmUiOiJXZWJBcHBEYXJrTW9kZVYyIiwiZXhwaXJ5IjoxNjkxNTM5MTk5fQ=="
    />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@ChromiumDev" />
    <meta name="twitter:creator" content="@tomayac" />
    <meta name="twitter:title" content="SVGcode" />
    <meta
      name="twitter:description"
      content="SVGcode is a Progressive Web App that lets you convert raster images like JPG, PNG, GIF, WebP, AVIF, etc. to vector graphics in SVG format."
    />
    <meta
      name="twitter:image"
      content="https://svgco.de/screenshots/desktop-dark.png"
    />

    <title>SVGcode—Convert raster images to SVG vector graphics</title>
    <link rel="stylesheet" href="/src/css/style.css" />
    <link
      rel="stylesheet"
      href="/src/css/dark.css"
      media="(prefers-color-scheme: dark)"
    />
    <link
      rel="stylesheet"
      href="/src/css/light.css"
      media="(prefers-color-scheme: light)"
    />
    <link rel="icon" type="image/svg" href="/favicon.svg" />
    <link rel="canonical" href="https://svgco.de/" />
    <link rel="apple-touch-icon" href="/favicon.png" />

    <script src="/src/js/main.js" type="module"></script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "SVGcode",
        "applicationCategory": "DesignApplication, BrowserApplication, MultimediaApplication, UtilitiesApplication",
        "screenshot": "https://svgco.de/screenshots/desktop-dark.png",
        "author": {
          "@type": "Person",
          "name": "Thomas Steiner",
          "email": "<EMAIL>",
          "image": "https://blog.tomayac.com/static/icon.png"
        },
        "offers": {
          "@type": "Offer",
          "price": "0"
        },
        "publisher": {
          "@type": "Organization",
          "name": "Blogccasion",
          "email": "<EMAIL>",
          "logo": {
            "@type": "ImageObject",
            "url": "https://blog.tomayac.com/static/icon.png"
          }
        }
      }
    </script>
    <base target="_blank" />
  </head>
  <body>
    <main>
      <details class="main">
        <summary></summary>
        <button type="button" class="with-bg close-options-button">
          <span aria-hidden="true">&#10005;</span>
        </button>
        <div class="sidebar">
          <form>
            <div class="preprocess">
              <div class="other-input">
                <input
                  id="color"
                  type="radio"
                  class="color"
                  name="output-option"
                  checked
                />
                <label for="color"></label>
              </div>
              <div class="other-input">
                <input
                  id="monochrome"
                  type="radio"
                  class="monochrome"
                  name="output-option"
                />
                <label for="monochrome"></label>
              </div>
              <div class="other-input">
                <input id="consider-dpr" type="checkbox" class="consider-dpr" />
                <label for="consider-dpr"></label>
              </div>
              <div class="other-input advanced">
                <input
                  id="optimize-curves"
                  type="checkbox"
                  class="optimize-curves"
                  checked
                />
                <label for="optimize-curves"></label>
              </div>
              <div class="other-input">
                <input
                  id="posterize"
                  type="checkbox"
                  class="posterize"
                  checked
                />
                <label for="posterize"></label>
              </div>
              <div class="other-input">
                <input
                  id="show-advanced"
                  type="checkbox"
                  class="show-advanced"
                />
                <label for="show-advanced"></label>
              </div>
              <div class="details"></div>
              <div class="other-input">
                <button type="button" class="reset-all"></button>
              </div>
            </div>
          </form>
          <ul class="link-list">
            <li>
              <a href="https://github.com/tomayac/SVGcode">GitHub</a>
            </li>
            <li>
              <a c href="https://twitter.com/ChromiumDev">Twitter</a>
            </li>
            <li>
              <a class="about" href="https://web.dev/svgcode/"></a>
            </li>
            <li>
              <a
                class="license"
                href="https://github.com/tomayac/SVGcode/blob/main/LICENSE"
              ></a>
            </li>
          </ul>
          <dark-mode-toggle
            permanent
            dark="Light Theme"
            light="Dark Theme"
          ></dark-mode-toggle>
          <select class="language"></select>
        </div>
      </details>
      <div class="main-area">
        <div class="menu">
          <button class="open menu" type="button"></button>
          <button class="save menu" type="button"></button>
          <button class="copy menu" type="button"></button>
          <button class="paste menu" type="button"></button>
          <button class="share menu" type="button"></button>
          <button class="install menu" type="button"></button>
          <progress hidden value="0" max="100"></progress>
          <label hidden><input class="debug" type="checkbox" /> Debug</label>
        </div>
        <img
          class="input-image"
          crossorigin="anonymous"
          src="/favicon.png"
          alt=""
          draggable="false"
        />
        <canvas></canvas>
        <div dir="ltr" class="pinch-zoom-wrapper">
          <pinch-zoom class="checkerboard">
            <div></div>
          </pinch-zoom>
          <svg version="1.0" xmlns="http://www.w3.org/2000/svg">
            <g class="svg-output"></g>
          </svg>
        </div>
        <div class="toast" hidden></div>
      </div>
    </main>
  </body>
</html>
