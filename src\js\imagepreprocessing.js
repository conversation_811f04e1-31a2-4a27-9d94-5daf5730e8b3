/**
 * SVGcode—Convert raster images to SVG vector graphics
 * Copyright (C) 2021 Google LLC
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 */

/**
 * Apply Gaussian blur to image data
 * @param {ImageData} imageData - Input image data
 * @param {number} radius - Blur radius
 * @returns {ImageData} Blurred image data
 */
const gaussianBlur = (imageData, radius = 1.0) => {
  if (radius <= 0) return imageData;

  const { width, height, data } = imageData;
  const output = new ImageData(width, height);
  const outputData = output.data;

  // Create Gaussian kernel
  const kernelSize = Math.ceil(radius * 3) * 2 + 1;
  const kernel = [];
  const sigma = radius / 3;
  let kernelSum = 0;

  for (let i = 0; i < kernelSize; i++) {
    const x = i - Math.floor(kernelSize / 2);
    const value = Math.exp(-(x * x) / (2 * sigma * sigma));
    kernel[i] = value;
    kernelSum += value;
  }

  // Normalize kernel
  for (let i = 0; i < kernelSize; i++) {
    kernel[i] /= kernelSum;
  }

  // Horizontal pass
  const tempData = new Uint8ClampedArray(data.length);
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      for (let c = 0; c < 4; c++) {
        let sum = 0;
        for (let k = 0; k < kernelSize; k++) {
          const px = Math.max(0, Math.min(width - 1, x + k - Math.floor(kernelSize / 2)));
          const idx = (y * width + px) * 4 + c;
          sum += data[idx] * kernel[k];
        }
        tempData[(y * width + x) * 4 + c] = sum;
      }
    }
  }

  // Vertical pass
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      for (let c = 0; c < 4; c++) {
        let sum = 0;
        for (let k = 0; k < kernelSize; k++) {
          const py = Math.max(0, Math.min(height - 1, y + k - Math.floor(kernelSize / 2)));
          const idx = (py * width + x) * 4 + c;
          sum += tempData[idx] * kernel[k];
        }
        outputData[(y * width + x) * 4 + c] = sum;
      }
    }
  }

  return output;
};

/**
 * Apply bilateral filter for edge-preserving smoothing
 * @param {ImageData} imageData - Input image data
 * @param {number} spatialSigma - Spatial sigma for distance weighting
 * @param {number} intensitySigma - Intensity sigma for color difference weighting
 * @returns {ImageData} Filtered image data
 */
const bilateralFilter = (imageData, spatialSigma = 2.0, intensitySigma = 20.0) => {
  const { width, height, data } = imageData;
  const output = new ImageData(width, height);
  const outputData = output.data;

  const kernelRadius = Math.ceil(spatialSigma * 3);
  const spatialCoeff = -0.5 / (spatialSigma * spatialSigma);
  const intensityCoeff = -0.5 / (intensitySigma * intensitySigma);

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const centerIdx = (y * width + x) * 4;
      const centerR = data[centerIdx];
      const centerG = data[centerIdx + 1];
      const centerB = data[centerIdx + 2];
      const centerA = data[centerIdx + 3];

      let sumR = 0, sumG = 0, sumB = 0, sumA = 0;
      let weightSum = 0;

      for (let dy = -kernelRadius; dy <= kernelRadius; dy++) {
        for (let dx = -kernelRadius; dx <= kernelRadius; dx++) {
          const nx = Math.max(0, Math.min(width - 1, x + dx));
          const ny = Math.max(0, Math.min(height - 1, y + dy));
          const nIdx = (ny * width + nx) * 4;

          const nR = data[nIdx];
          const nG = data[nIdx + 1];
          const nB = data[nIdx + 2];
          const nA = data[nIdx + 3];

          // Spatial weight
          const spatialDist = dx * dx + dy * dy;
          const spatialWeight = Math.exp(spatialDist * spatialCoeff);

          // Intensity weight
          const intensityDist = (centerR - nR) * (centerR - nR) +
                               (centerG - nG) * (centerG - nG) +
                               (centerB - nB) * (centerB - nB);
          const intensityWeight = Math.exp(intensityDist * intensityCoeff);

          const weight = spatialWeight * intensityWeight;

          sumR += nR * weight;
          sumG += nG * weight;
          sumB += nB * weight;
          sumA += nA * weight;
          weightSum += weight;
        }
      }

      if (weightSum > 0) {
        outputData[centerIdx] = sumR / weightSum;
        outputData[centerIdx + 1] = sumG / weightSum;
        outputData[centerIdx + 2] = sumB / weightSum;
        outputData[centerIdx + 3] = sumA / weightSum;
      } else {
        outputData[centerIdx] = centerR;
        outputData[centerIdx + 1] = centerG;
        outputData[centerIdx + 2] = centerB;
        outputData[centerIdx + 3] = centerA;
      }
    }
  }

  return output;
};

/**
 * Apply morphological opening (erosion followed by dilation)
 * @param {ImageData} imageData - Input image data
 * @param {number} kernelSize - Size of morphological kernel
 * @returns {ImageData} Processed image data
 */
const morphologicalOpening = (imageData, kernelSize = 3) => {
  const eroded = morphologicalErosion(imageData, kernelSize);
  return morphologicalDilation(eroded, kernelSize);
};

/**
 * Apply morphological erosion
 * @param {ImageData} imageData - Input image data
 * @param {number} kernelSize - Size of morphological kernel
 * @returns {ImageData} Eroded image data
 */
const morphologicalErosion = (imageData, kernelSize = 3) => {
  const { width, height, data } = imageData;
  const output = new ImageData(width, height);
  const outputData = output.data;
  const radius = Math.floor(kernelSize / 2);

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      let minR = 255, minG = 255, minB = 255, minA = 255;

      for (let dy = -radius; dy <= radius; dy++) {
        for (let dx = -radius; dx <= radius; dx++) {
          const nx = Math.max(0, Math.min(width - 1, x + dx));
          const ny = Math.max(0, Math.min(height - 1, y + dy));
          const idx = (ny * width + nx) * 4;

          minR = Math.min(minR, data[idx]);
          minG = Math.min(minG, data[idx + 1]);
          minB = Math.min(minB, data[idx + 2]);
          minA = Math.min(minA, data[idx + 3]);
        }
      }

      const outIdx = (y * width + x) * 4;
      outputData[outIdx] = minR;
      outputData[outIdx + 1] = minG;
      outputData[outIdx + 2] = minB;
      outputData[outIdx + 3] = minA;
    }
  }

  return output;
};

/**
 * Apply morphological dilation
 * @param {ImageData} imageData - Input image data
 * @param {number} kernelSize - Size of morphological kernel
 * @returns {ImageData} Dilated image data
 */
const morphologicalDilation = (imageData, kernelSize = 3) => {
  const { width, height, data } = imageData;
  const output = new ImageData(width, height);
  const outputData = output.data;
  const radius = Math.floor(kernelSize / 2);

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      let maxR = 0, maxG = 0, maxB = 0, maxA = 0;

      for (let dy = -radius; dy <= radius; dy++) {
        for (let dx = -radius; dx <= radius; dx++) {
          const nx = Math.max(0, Math.min(width - 1, x + dx));
          const ny = Math.max(0, Math.min(height - 1, y + dy));
          const idx = (ny * width + nx) * 4;

          maxR = Math.max(maxR, data[idx]);
          maxG = Math.max(maxG, data[idx + 1]);
          maxB = Math.max(maxB, data[idx + 2]);
          maxA = Math.max(maxA, data[idx + 3]);
        }
      }

      const outIdx = (y * width + x) * 4;
      outputData[outIdx] = maxR;
      outputData[outIdx + 1] = maxG;
      outputData[outIdx + 2] = maxB;
      outputData[outIdx + 3] = maxA;
    }
  }

  return output;
};

/**
 * Apply pre-processing filters to image data
 * @param {ImageData} imageData - Input image data
 * @param {Object} options - Processing options
 * @returns {ImageData} Processed image data
 */
const preprocessImage = (imageData, options = {}) => {
  const {
    enableGaussianBlur = false,
    gaussianBlurRadius = 1.0,
    enableBilateralFilter = false,
    bilateralSpatialSigma = 2.0,
    bilateralIntensitySigma = 20.0,
    enableMorphological = false,
    morphologicalKernelSize = 3
  } = options;

  let result = imageData;

  try {
    // Apply Gaussian blur if enabled
    if (enableGaussianBlur && gaussianBlurRadius > 0) {
      result = gaussianBlur(result, gaussianBlurRadius);
    }

    // Apply bilateral filter if enabled
    if (enableBilateralFilter) {
      result = bilateralFilter(result, bilateralSpatialSigma, bilateralIntensitySigma);
    }

    // Apply morphological opening if enabled
    if (enableMorphological && morphologicalKernelSize > 0) {
      result = morphologicalOpening(result, morphologicalKernelSize);
    }

    return result;
  } catch (error) {
    console.warn('Error in image preprocessing:', error);
    return imageData; // Return original on error
  }
};

export { preprocessImage, gaussianBlur, bilateralFilter, morphologicalOpening };
