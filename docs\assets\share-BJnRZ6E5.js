import{c as r,e as l,f as i,g as o,o as g,h as c,j as m,F as f,k as d}from"./index-aEZOVBVk.js";r.style.display="flex";r.addEventListener("click",async()=>{let a=l.innerHTML;i(o.t("optimizingSVG"),1/0),a=await g(a),c();let t=!1;try{t=await m(f)}catch{}const n=t?d(t):"Untitled.svg",s={files:[new File([a],n,{type:"image/svg+xml"})]};if(navigator.canShare(s))try{await navigator.share(s)}catch(e){e.name!=="AbortError"&&(console.error(e.name,e.message),i(e.message))}});
