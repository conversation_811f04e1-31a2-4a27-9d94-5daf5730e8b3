/**
 * SVGcode—Convert raster images to SVG vector graphics
 * Copyright (C) 2021 Google LLC
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 */

import languages from './languages.js';

const translations = {
  red: 'Red',
  green: 'Green',
  blue: 'Blue',
  alpha: 'Alpha',

  brightness: 'Brightness',
  contrast: 'Contrast',
  grayscale: 'Grayscale',
  'hue-rotate': 'Hue Rotate',
  invert: 'Invert',
  opacity: 'Opacity',
  saturate: 'Saturate',
  sepia: 'Sepia',

  scale: 'Scale',
  rotation: 'Rotation',
  turdsize: 'Suppress Speckles',
  alphamax: 'Corner Threshold',
  minPathSegments: 'Min. Path Length',
  strokeWidth: 'Stroke Width',
  turnpolicy: 'Turn Policy',
  opticurve: 'Optimize Curves',
  opttolerance: 'Optimization Tolerance',
  showAdvancedControls: 'Show Expert Options',

  '%': '%',
  deg: '°',
  steps: 'Steps',
  pixels: 'Pixels',
  segments: 'Segments',

  reset: 'Reset',
  resetAll: 'Reset All',

  dropFileHere: 'Drop File Here',
  openImage: 'Open Image',
  saveSVG: 'Save SVG',
  pasteImage: 'Paste Image',
  copySVG: 'Copy SVG',
  shareSVG: 'Share SVG',
  install: 'Install',

  posterizeInputImage: 'Posterize Input Image',
  colorSVG: 'Color SVG',
  monochromeSVG: 'Monochrome SVG',

  colorChannels: 'Color Channels',
  imageSizeAndRotation: 'Input Size and Rotation',
  imagePreprocessing: 'Input Preprocessing',
  svgOptions: 'SVG Options',

  considerDPR: 'Consider Device Pixel Ratio',

  tweak: 'Tweak',
  closeOptions: 'Close',

  optimizingSVG: 'Optimizing SVG',
  copiedSVG: 'Copied SVG',
  savedSVG: 'Saved SVG',

  readyToWorkOffline: 'Ready to Work Offline.',
  svgSize: 'SVG Size',
  zoom: 'Zoom',

  license: 'License',
  about: 'About',

  ...languages,
};

// ignore unused exports default
export default translations;
