/**
 * SVGcode—Convert raster images to SVG vector graphics
 * Copyright (C) 2021 Google LLC
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 */

:root {
  /* stylelint-disable custom-property-pattern */
  --100vh: 100vh;
  --space: 0.25rem;
  --mobile-breakpoint: 414px;
  --menu-height: 5ex;
  --sidebar-width: 15rem;
  --input-image-height: 5rem;
  --input-width: 10rem;
  --color-scheme: dark light;
  --focus-ring-color: -webkit-focus-ring-color;

  /* Defined in `light.css` and `dark.css` */
  accent-color: var(--main-accent-color);
  color-scheme: var(--color-scheme);
  font-family: system-ui, sans-serif;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

html,
body {
  overscroll-behavior: none;
  overflow: hidden;
  width: 100vw;
  max-width: 100vw;
  height: var(--100vh);
  max-height: var(--100vh);
  background-color: var(--canvas);
}

body {
  margin: 0;
  padding: 0;
}

main {
  display: flex;
  height: 100%;
}

main.window-controls-overlay {
  margin-block-start: env(titlebar-area-height);
}

progress {
  min-width: 10rem;
}

canvas {
  display: none;
}

canvas.debug {
  --scale-factor: 2;

  width: auto;
  height: var(--input-image-height);
  display: block;
  margin-inline-start: var(--space);
  /* stylelint-disable-next-line */
  margin-block-start: calc(
    var(--menu-height) + 4 * var(--space) + var(--input-image-height)
  );
  position: absolute;
  z-index: 1;
  cursor: default;
  inset-inline-end: var(--space);
}

input[type="range"] {
  display: inline-block;
  width: var(--input-width);
}

summary,
label,
button,
details {
  font-family: inherit;
  font-size: inherit;
  white-space: nowrap;
  user-select: none;
  cursor: pointer;
  margin-block-end: var(--space);
}

details {
  margin-block-end: calc(2 * var(--space));
  padding-inline-end: var(--space);
}

summary {
  color: var(--alternative-accent-color);
  display: flex;
  align-content: center;
  margin-block: calc(2 * var(--space));
}

summary::-webkit-details-marker {
  display: none;
}

summary::marker {
  content: "";
}

.main > summary {
  display: none;
}

details[open] {
  flex-shrink: 0;
}

summary::after {
  margin-inline-start: var(--space);
}

[dir="ltr"] details:not([open], .main) > summary::after {
  content: "▶";
}

[dir="rtl"] details:not([open], .main) > summary::after {
  content: "◀️";
}

details[open]:not(.main) > summary::after {
  content: "▼";
}

details.main {
  z-index: 1;
  height: 100%;
}

details.main .close-options-button {
  display: none;
  position: fixed;
  margin: 0.5rem 0 0;
  width: 2.2rem;
  height: 2.2rem;
  z-index: 1;
  font-size: 1rem;
  inset-inline-end: 1.25rem;
}

@media (width <= 414px) {
  summary,
  label,
  button,
  details {
    font-size: 0.9rem;
  }

  details.main {
    position: absolute;
    margin-inline-start: var(--space);
    margin-block-start: calc(var(--menu-height) + 4 * var(--space));
    padding: var(--space);
    z-index: 2;
  }

  details[open].main {
    width: 100vw;
    max-height: calc(var(--100vh) - var(--menu-height) - 9 * var(--space));
  }

  details[open].main > .sidebar {
    max-width: calc(100% - var(--space) * 4);
    /* stylelint-disable-next-line */
    -webkit-backdrop-filter: saturate(180%) blur(100px);
    backdrop-filter: saturate(180%) blur(100px);
    background-color: transparent;
  }

  details.main > summary {
    display: flex;
    align-items: center;
    place-content: center;
    background-color: var(--canvas);
    border: solid 1px currentcolor;
    border-radius: var(--space);
    padding: var(--space);
    cursor: pointer;
  }

  /* Make the summary occupy the whole space so clicking anywhere closes. */
  details.main[open] > summary {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: var(--100vh);
    cursor: auto;
    z-index: -1;
    opacity: 0;
  }

  details.main[open] .close-options-button {
    display: inline-block;
  }
}

.input-image {
  width: auto;
  height: var(--input-image-height);
  object-fit: contain;
  position: absolute;
  margin-inline-start: var(--space);
  margin-block-start: calc(var(--menu-height) + 2 * var(--space));
  z-index: 1;
  cursor: default;
  user-select: none;
  inset-inline-end: var(--space);
}

.input-image:hover {
  /* stylelint-disable-next-line */
  transform: translateX(calc(var(--scale-factor, 0) * 50%))
    scale(calc(var(--scale-factor, 1) * 2));
}

canvas.debug:hover,
.input-image:hover {
  z-index: 2;
  cursor: zoom-in;
}

[dir="ltr"] canvas.debug,
[dir="ltr"] .input-image {
  transform-origin: top right;
}

[dir="rtl"] canvas.debug,
[dir="rtl"] .input-image {
  transform-origin: top left;
}

[dir="ltr"] canvas.debug {
  transform: translateX(50%) scale(var(--scale-factor));
}

[dir="rtl"] canvas.debug {
  transform: translateX(-50%) scale(var(--scale-factor));
}

[dir="ltr"] canvas.debug:hover {
  /* stylelint-disable-next-line */
  transform: translateX(calc(var(--scale-factor, 0) * 50%))
    scale(calc(var(--scale-factor, 1) * 2));
}

[dir="rtl"] canvas.debug:hover {
  /* stylelint-disable-next-line */
  transform: translateX(calc(var(--scale-factor, 0) * -50%))
    scale(calc(var(--scale-factor, 1) * 2));
}

summary svg {
  fill: var(--alternative-accent-color);
}

.svg-output {
  color: var(--canvas-text);
}

.checkerboard {
  background-position:
    0 0,
    calc(2 * var(--space)) calc(2 * var(--space));
  background-size: calc(4 * var(--space)) calc(4 * var(--space));
}

.pinch-zoom-wrapper {
  width: 100%;
  height: 100%;
}

pinch-zoom {
  width: 100%;
  height: 100%;
  position: absolute;
  cursor: grab;
}

pinch-zoom > * {
  position: absolute;
  inset: 0;
  visibility: hidden;
}

.pinch-zoom-wrapper > svg {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/*
@ToDo: The performance goes down when the stroke-width is > 0.

.pinch-zoom-wrapper > svg:hover:not(.interactive) path:not(:hover) {
  opacity: 50%;
}

.pinch-zoom-wrapper > svg:hover:not(.interactive) path:hover {
  opacity: 100%;
}
*/

.icon {
  margin-inline-end: var(--space);
  display: flex;
}

.icon svg {
  width: 2.5ex;
  height: 2.5ex;
  fill: currentcolor;
  background-color: transparent;
}

label {
  font-variant-numeric: tabular-nums;
  color: var(--canvas-text);
}

select,
button {
  height: 3ex;
  align-items: center;
  place-content: center;
  background-color: transparent;
  color: var(--canvas-text);
  border: solid 1px currentcolor;
  border-radius: var(--space);
  margin-inline-end: var(--space);
  cursor: pointer;
  padding: 1px 6px;
}

select {
  flex-shrink: 0;
}

button.menu {
  border: none;
  width: max-content;
  flex-shrink: 0;
  margin-inline-end: calc(4 * var(--space));
}

button.menu > span:last-of-type {
  direction: ltr;
}

[data-i18n-key="svgOptions"],
label[for="color"],
label[for="monochrome"] {
  direction: ltr;
}

button.share,
button.install {
  display: none;
}

summary:hover,
label:hover,
button:hover {
  color: var(--main-accent-color);
  border-color: var(--main-accent-color);
}

.preprocess {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.preprocess-input {
  display: flex;
  flex-direction: column;
  margin: var(--space);
}

.preprocess-input > div {
  display: flex;
  gap: var(--space);
}

.other-input {
  display: flex;
  margin: var(--space);
  height: 3ex;
  align-items: flex-start;
  gap: var(--space);
}

.other-input.advanced,
.preprocess-input.advanced {
  display: none;
}

.sidebar {
  overflow: hidden auto;
  scrollbar-width: none;
  display: flex;
  flex-direction: column;
  padding: var(--space);
  gap: var(--space);
  min-width: var(--sidebar-width);
  height: 100%;
  background-color: var(--canvas);
}

.sidebar::-webkit-scrollbar {
  display: none;
}

.link-list {
  list-style: none;
  margin-block: var(--space);
  padding-inline-start: var(--space);
  line-height: 2;
}

.link-list > li {
  display: inline-block;
}

.link-list > li:not(:last-of-type)::after {
  content: "•";
}

.link-list a {
  color: var(--canvas-text);
  text-decoration: none;
}

.link-list a:hover {
  color: var(--main-accent-color);
}

.menu {
  width: 100%;
  height: var(--menu-height);
  flex-shrink: 0;
  align-items: center;
  display: flex;
  gap: var(--space);
  overflow: auto hidden;
  scrollbar-width: none;
}

.menu.window-controls-overlay {
  position: absolute;
  padding-block-start: calc(2 * var(--space));
  left: env(titlebar-area-x);
  top: env(titlebar-area-y);
  width: env(titlebar-area-width);
  height: env(titlebar-area-height);
  -webkit-app-region: drag;
}

.window-controls-overlay button,
.window-controls-overlay label {
  -webkit-app-region: no-drag;
  font-size: 0.9rem;
}

.menu::-webkit-scrollbar {
  display: none;
}

.main-area {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--space);
  flex-grow: 1;
  overflow: hidden;
}

.dropenter::before {
  display: grid;
  place-items: center;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  content: attr(data-drop-text);
  position: absolute;
  z-index: 2;
}

.toast {
  color: var(--canvas-text);
  background-color: var(--canvas);
  align-self: center;
  border: solid 1px var(--canvas-text);
  padding: var(--space);
  position: absolute;
  bottom: 5ex;
}

.language {
  max-width: fit-content;
  font-size: inherit;
  margin-block: calc(2 * var(--space));
}

.language:hover {
  color: var(--main-accent-color);
  border-color: var(--main-accent-color);
}

dark-mode-toggle {
  --dark-mode-toggle-light-icon: url("/moon.svg");
  --dark-mode-toggle-dark-icon: url("/sun.svg");
  --dark-mode-toggle-color: var(--canvas-text);
  --dark-mode-toggle-icon-filter: invert(100%);

  border: solid 1px var(--canvas-text);
  border-radius: var(--space);
  max-width: fit-content;
  margin-block: calc(2 * var(--space));
}

dark-mode-toggle:focus-within {
  outline: solid 2px var(--focus-ring-color, var(--main-accent-color));
  outline-offset: -1px;
}

dark-mode-toggle:focus::part(toggleLabel) {
  outline: none;
}

dark-mode-toggle:hover {
  --dark-mode-toggle-color: var(--main-accent-color);

  border-color: var(--main-accent-color);
}
