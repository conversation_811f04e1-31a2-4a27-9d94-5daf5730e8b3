/**
 * SVGcode—Convert raster images to SVG vector graphics
 * Copyright (C) 2021 Google LLC
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 */

import languages from './languages.js';

const translations = {
  red: 'Rood',
  green: 'Groen',
  blue: 'Blauw',
  alpha: 'Alfa',

  brightness: 'Helderheid',
  contrast: 'Contrast',
  grayscale: 'Grijswaarden',
  'hue-rotate': '<PERSON>e Rotate',
  invert: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  opacity: 'Transparantie',
  saturate: 'Verzadiging',
  sepia: 'Sepia',

  scale: 'Schaal',
  rotation: 'Rotatie',
  turdsize: 'Onderdruk spikkels',
  alphamax: 'Corner Threshold',
  minPathSegments: 'Min. Padlengte',
  strokeWidth: 'Lijndikte',
  turnpolicy: 'Turn Policy',
  opticurve: 'Optimaliseer Curves',
  opttolerance: 'Optimalisatie Tolerantie',
  showAdvancedControls: 'Toon Expert Opties',

  '%': '%',
  deg: '°',
  steps: 'Stappen',
  pixels: 'Pixels',
  segments: 'Segmenten',

  reset: 'Reset',
  resetAll: 'Reset Alles',

  dropFileHere: 'Drop bestand hier',
  openImage: 'Open beeldbestand',
  saveSVG: 'SVG opslaan',
  pasteImage: 'Beeldbestand plakken',
  copySVG: 'SVG kopiëren',
  shareSVG: 'SVG delen',
  install: 'Installeren',

  posterizeInputImage: 'Invoerbeeld Posteriseren',
  colorSVG: 'Kleuren SVG',
  monochromeSVG: 'Monochrome SVG',

  colorChannels: 'Kleurkanalen',
  imageSizeAndRotation: 'Invoergrootte en Rotatie',
  imagePreprocessing: 'Invoer Voorbewerking',
  svgOptions: 'SVG Opties',

  considerDPR: 'Consider Device Pixel Ratio',

  tweak: 'Tweak',
  closeOptions: 'Sluiten',

  optimizingSVG: 'SVG aan het optimaliseren',
  copiedSVG: 'SVG gekopiëerd',
  savedSVG: 'SVG opgeslagen',

  readyToWorkOffline: 'Klaar om offline te werken.',
  svgSize: 'SVG grootte',
  zoom: 'Inzoomen',

  license: 'Licentie',
  about: 'Over',

  ...languages,
};

// ignore unused exports default
export default translations;
