{"arrowParens": "always", "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "bracketSameLine": false, "jsxSingleQuote": false, "printWidth": 80, "proseWrap": "always", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": true, "overrides": [{"files": ["**/*.css", "**/*.scss", "**/*.html"], "options": {"singleQuote": false}}], "tabWidth": 2, "trailingComma": "all", "useTabs": false, "vueIndentScriptAndStyle": false}