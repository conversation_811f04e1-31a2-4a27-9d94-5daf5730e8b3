import i from"./languages-BtQEISXM.js";const e={red:"<PERSON><PERSON>",green:"<PERSON>",blue:"Blu",alpha:"Alfa",brightness:"Luminosità",contrast:"Contras<PERSON>",grayscale:"Scala di grigi","hue-rotate":"Variazione di tinta",invert:"Inversione colori",opacity:"Opacità",saturate:"Saturazione",sepia:"Effetto seppia",scale:"Scala",rotation:"Ruota",turdsize:"Elimina difetti",alphamax:"Soglia d'angolo",minPathSegments:"Lunghezza min. tratto",strokeWidth:"Larghezza del tratto",turnpolicy:"Disambiguazione tratti",opticurve:"Ottimizza le curve",opttolerance:"Tolleranza ottimizzazione",showAdvancedControls:"Mostra opzioni avanzate","%":"%",deg:"°",steps:"Passi",pixels:"Pixel",segments:"Segmenti",reset:"Ripristina",resetAll:"Ripristina tutto",dropFileHere:"Trascina un file qui",openImage:"Apri immagine",saveSVG:"Salva SVG",pasteImage:"Incolla immagine",copySVG:"Copia SVG",shareSVG:"Condividi SVG",install:"Installa",posterizeInputImage:"Posterizza immagine di input",colorSVG:"SVG a colori",monochromeSVG:"SVG monocromatico",colorChannels:"Canali di colore",imageSizeAndRotation:"Dimensioni e rotazione dell'immagine",imagePreprocessing:"Preelaborazione immagine",svgOptions:"Opzioni SVG",considerDPR:"Considera il rapporto pixel del dispositivo",tweak:"Modifica",closeOptions:"Chiudi",optimizingSVG:"Ottimizzazione SVG",copiedSVG:"SVG copiato",savedSVG:"SVG salvato",readyToWorkOffline:"Pronto per lavorare offline.",svgSize:"Dimensioni SVG",zoom:"Zoom",license:"Licenza",about:"Informazioni",...i};export{e as default};
