(function(){"use strict";var c,g=g!==void 0?g:{},e={};for(c in g)g.hasOwnProperty(c)&&(e[c]=g[c]);var j,b,u,Y,k,X=function(A,I){throw I},CA=typeof window=="object",J=typeof importScripts=="function",v=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",M="";v?(M=J?require("path").dirname(M)+"/":__dirname+"/",j=function(A,I){var C=K(A);return C?I?C:C.toString():(Y||(Y=require("fs")),k||(k=require("path")),A=k.normalize(A),Y.readFileSync(A,I?null:"utf8"))},u=function(A){var I=j(A,!0);return I.buffer||(I=new Uint8Array(I)),BA(I.buffer),I},b=function(A,I,C){var B=K(A);B&&I(B),Y||(Y=require("fs")),k||(k=require("path")),A=k.normalize(A),Y.readFile(A,function(Q,E){Q?C(Q):I(E.buffer)})},process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),typeof module<"u"&&(module.exports=g),process.on("uncaughtException",function(A){if(!(A instanceof AA))throw A}),process.on("unhandledRejection",function(A){throw A}),X=function(A,I){if(RA())throw process.exitCode=A,I;var C;(C=I)instanceof AA||r("exiting due to exception: "+C),process.exit(A)},g.inspect=function(){return"[Emscripten Module object]"}):(CA||J)&&(J?M=self.location.href:typeof document<"u"&&document.currentScript&&(M=document.currentScript.src),M=M.indexOf("blob:")!==0?M.substr(0,M.replace(/[?#].*/,"").lastIndexOf("/")+1):"",j=function(A){try{var I=new XMLHttpRequest;return I.open("GET",A,!1),I.send(null),I.responseText}catch(B){var C=K(A);if(C)return function(Q){for(var E=[],D=0;D<Q.length;D++){var o=Q[D];o>255&&(o&=255),E.push(String.fromCharCode(o))}return E.join("")}(C);throw B}},J&&(u=function(A){try{var I=new XMLHttpRequest;return I.open("GET",A,!1),I.responseType="arraybuffer",I.send(null),new Uint8Array(I.response)}catch(B){var C=K(A);if(C)return C;throw B}}),b=function(A,I,C){var B=new XMLHttpRequest;B.open("GET",A,!0),B.responseType="arraybuffer",B.onload=function(){if(B.status==200||B.status==0&&B.response)I(B.response);else{var Q=K(A);Q?I(Q.buffer):C()}},B.onerror=C,B.send(null)});var L,YA=g.print||console.log.bind(console),r=g.printErr||console.warn.bind(console);for(c in e)e.hasOwnProperty(c)&&(g[c]=e[c]);e=null,g.arguments&&g.arguments,g.thisProgram&&g.thisProgram,g.quit&&(X=g.quit),g.wasmBinary&&(L=g.wasmBinary);var O,kA=g.noExitRuntime||!0;typeof WebAssembly!="object"&&x("no native wasm support detected");var V=!1;function BA(A,I){A||x("Assertion failed: "+I)}function QA(A){var I=g["_"+A];return BA(I,"Cannot call unknown function "+A+", make sure it is exported"),I}function UA(A,I,C,B,Q){var E={string:function(R){var i=0;if(R!=null&&R!==0){var w=1+(R.length<<2);(function(t,F,l){(function(S,N,y,SA){if(!(SA>0))return 0;for(var HA=y,T=y+SA-1,W=0;W<S.length;++W){var s=S.charCodeAt(W);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&S.charCodeAt(++W)),s<=127){if(y>=T)break;N[y++]=s}else if(s<=2047){if(y+1>=T)break;N[y++]=192|s>>6,N[y++]=128|63&s}else if(s<=65535){if(y+2>=T)break;N[y++]=224|s>>12,N[y++]=128|s>>6&63,N[y++]=128|63&s}else{if(y+3>=T)break;N[y++]=240|s>>18,N[y++]=128|s>>12&63,N[y++]=128|s>>6&63,N[y++]=128|63&s}}N[y]=0})(t,U,F,l)})(R,i=$(w),w)}return i},array:function(R){var i=$(R.length);return function(w,t){iA.set(w,t)}(R,i),i}},D=QA(A),o=[],G=0;if(B)for(var a=0;a<B.length;a++){var d=E[C[a]];d?(G===0&&(G=cA()),o[a]=d(B[a])):o[a]=B[a]}var q=D.apply(null,o);return q=function(R){return G!==0&&rA(G),function(i){return I==="string"?GA(i):I==="boolean"?!!i:i}(R)}(q)}var EA,iA,U,H,DA=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function oA(A,I,C){for(var B=I+C,Q=I;A[Q]&&!(Q>=B);)++Q;if(Q-I>16&&A.subarray&&DA)return DA.decode(A.subarray(I,Q));for(var E="";I<Q;){var D=A[I++];if(128&D){var o=63&A[I++];if((224&D)!=192){var G=63&A[I++];if((D=(240&D)==224?(15&D)<<12|o<<6|G:(7&D)<<18|o<<12|G<<6|63&A[I++])<65536)E+=String.fromCharCode(D);else{var a=D-65536;E+=String.fromCharCode(55296|a>>10,56320|1023&a)}}else E+=String.fromCharCode((31&D)<<6|o)}else E+=String.fromCharCode(D)}return E}function GA(A,I){return A?oA(U,A,I):""}function aA(A){EA=A,g.HEAP8=iA=new Int8Array(A),g.HEAP16=new Int16Array(A),g.HEAP32=H=new Int32Array(A),g.HEAPU8=U=new Uint8Array(A),g.HEAPU16=new Uint16Array(A),g.HEAPU32=new Uint32Array(A),g.HEAPF32=new Float32Array(A),g.HEAPF64=new Float64Array(A)}g.INITIAL_MEMORY;var yA,sA=[],wA=[],hA=[],tA=!1;function RA(){return kA||!1}var n=0,f=null;function x(A){throw g.onAbort&&g.onAbort(A),r(A="Aborted("+A+")"),V=!0,A+=". Build with -s ASSERTIONS=1 for more info.",new WebAssembly.RuntimeError(A)}g.preloadedImages={},g.preloadedAudios={};var h,z;function P(A){return A.startsWith("data:application/octet-stream;base64,")}function FA(A){return A.startsWith("file://")}function NA(A){try{if(A==h&&L)return new Uint8Array(L);var I=K(A);if(I)return I;if(u)return u(A);throw"both async and sync fetching of the wasm failed"}catch(C){x(C)}}function _(A){for(;A.length>0;){var I=A.shift();if(typeof I!="function"){var C=I.func;typeof C=="number"?I.arg===void 0?MA(C)():MA(C)(I.arg):C(I.arg===void 0?null:I.arg)}else I(g)}}P(h="data:application/octet-stream;base64,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")||(z=h,h=g.locateFile?g.locateFile(z,M):M+z);var m=[];function MA(A){var I=m[A];return I||(A>=m.length&&(m.length=A+1),m[A]=I=yA.get(A)),I}function KA(A){try{return O.grow(A-EA.byteLength+65535>>>16),aA(O.buffer),1}catch{}}var Z={mappings:{},buffers:[null,[],[]],printChar:function(A,I){var C=Z.buffers[A];I===0||I===10?((A===1?YA:r)(oA(C,0)),C.length=0):C.push(I)},varargs:void 0,get:function(){return Z.varargs+=4,H[Z.varargs-4>>2]},getStr:function(A){return GA(A)},get64:function(A,I){return A}},eA=typeof atob=="function"?atob:function(A){var I,C,B,Q,E,D,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",G="",a=0;A=A.replace(/[^A-Za-z0-9\+\/\=]/g,"");do I=o.indexOf(A.charAt(a++))<<2|(Q=o.indexOf(A.charAt(a++)))>>4,C=(15&Q)<<4|(E=o.indexOf(A.charAt(a++)))>>2,B=(3&E)<<6|(D=o.indexOf(A.charAt(a++))),G+=String.fromCharCode(I),E!==64&&(G+=String.fromCharCode(C)),D!==64&&(G+=String.fromCharCode(B));while(a<A.length);return G};function K(A){if(P(A))return function(I){if(typeof v=="boolean"&&v){var C=Buffer.from(I,"base64");return new Uint8Array(C.buffer,C.byteOffset,C.byteLength)}try{for(var B=eA(I),Q=new Uint8Array(B.length),E=0;E<B.length;++E)Q[E]=B.charCodeAt(E);return Q}catch{throw new Error("Converting base64 string to bytes failed.")}}(A.slice(37))}var p,JA={c:function(A,I,C){U.copyWithin(A,I,I+C)},d:function(A){var I,C,B=U.length,Q=2147483648;if((A>>>=0)>Q)return!1;for(var E=1;E<=4;E*=2){var D=B*(1+.2/E);if(D=Math.min(D,A+100663296),KA(Math.min(Q,((I=Math.max(A,D))%(C=65536)>0&&(I+=C-I%C),I))))return!0}return!1},f:function(A){(function(I,C){(function(B){RA()||(g.onExit&&g.onExit(B),V=!0),X(B,new AA(B))})(I)})(A)},e:function(A){return 0},b:function(A,I,C,B,Q){},a:function(A,I,C,B){for(var Q=0,E=0;E<C;E++){var D=H[I>>2],o=H[I+4>>2];I+=8;for(var G=0;G<o;G++)Z.printChar(A,U[D+G]);Q+=o}return H[B>>2]=Q,0}},cA=(function(){var A={a:JA};function I(Q,E){var D,o=Q.exports;g.asm=o,aA((O=g.asm.g).buffer),yA=g.asm.j,D=g.asm.h,wA.unshift(D),function(G){if(n--,g.monitorRunDependencies&&g.monitorRunDependencies(n),n==0&&f){var a=f;f=null,a()}}()}function C(Q){I(Q.instance)}function B(Q){return function(){if(!L&&(CA||J)){if(typeof fetch=="function"&&!FA(h))return fetch(h,{credentials:"same-origin"}).then(function(E){if(!E.ok)throw"failed to load wasm binary file at '"+h+"'";return E.arrayBuffer()}).catch(function(){return NA(h)});if(b)return new Promise(function(E,D){b(h,function(o){E(new Uint8Array(o))},D)})}return Promise.resolve().then(function(){return NA(h)})}().then(function(E){return WebAssembly.instantiate(E,A)}).then(function(E){return E}).then(Q,function(E){r("failed to asynchronously prepare wasm: "+E),x(E)})}if(n++,g.monitorRunDependencies&&g.monitorRunDependencies(n),g.instantiateWasm)try{return g.instantiateWasm(A,I)}catch(Q){return r("Module.instantiateWasm callback failed with error: "+Q),!1}L||typeof WebAssembly.instantiateStreaming!="function"||P(h)||FA(h)||typeof fetch!="function"?B(C):fetch(h,{credentials:"same-origin"}).then(function(Q){return WebAssembly.instantiateStreaming(Q,A).then(C,function(E){return r("wasm streaming compile failed: "+E),r("falling back to ArrayBuffer instantiation"),B(C)})})}(),g.___wasm_call_ctors=function(){return(g.___wasm_call_ctors=g.asm.h).apply(null,arguments)},g._start=function(){return(g._start=g.asm.i).apply(null,arguments)},g.stackSave=function(){return(cA=g.stackSave=g.asm.k).apply(null,arguments)}),rA=g.stackRestore=function(){return(rA=g.stackRestore=g.asm.l).apply(null,arguments)},$=g.stackAlloc=function(){return($=g.stackAlloc=g.asm.m).apply(null,arguments)};function AA(A){this.name="ExitStatus",this.message="Program terminated with exit("+A+")",this.status=A}function IA(A){function I(){p||(p=!0,g.calledRun=!0,V||(tA=!0,_(wA),g.onRuntimeInitialized&&g.onRuntimeInitialized(),function(){if(g.postRun)for(typeof g.postRun=="function"&&(g.postRun=[g.postRun]);g.postRun.length;)C=g.postRun.shift(),hA.unshift(C);var C;_(hA)}()))}n>0||(function(){if(g.preRun)for(typeof g.preRun=="function"&&(g.preRun=[g.preRun]);g.preRun.length;)C=g.preRun.shift(),sA.unshift(C);var C;_(sA)}(),n>0||(g.setStatus?(g.setStatus("Running..."),setTimeout(function(){setTimeout(function(){g.setStatus("")},1),I()},1)):I()))}if(f=function A(){p||IA(),p||(f=A)},g.run=IA,g.preInit)for(typeof g.preInit=="function"&&(g.preInit=[g.preInit]);g.preInit.length>0;)g.preInit.pop()();IA();const nA=async(A,I={})=>{I=Object.assign({turdsize:2,turnpolicy:4,alphamax:1,opticurve:1,opttolerance:.2,pathonly:!1},I);const C=(i,w,t,F)=>F&&.2126*i+.7152*w+.0722*t<128;let B;const Q=A.constructor.name;if(Q==="Blob")B=await(async()=>new Promise(i=>{const w=URL.createObjectURL(A),t=new Image;t.onload=()=>{const F=document.createElement("canvas"),l=F.getContext("2d");if(!l)throw new Error("Canvas is not supported.");const S=t;F.width=Number(S.width),F.height=Number(S.height),l.drawImage(S,0,0,F.width,F.height),i(l.getImageData(0,0,F.width,F.height))},t.src=w}))();else if(Q==="HTMLImageElement"||Q==="SVGImageElement"||Q==="HTMLVideoElement"||Q==="HTMLCanvasElement"||Q==="ImageBitmap"){const i=document.createElement("canvas"),w=i.getContext("2d");if(!w)throw new Error("Canvas is not supported.");const t=A;i.width=Number(t.width),i.height=Number(t.height),w.drawImage(t,0,0,i.width,i.height),B=w.getImageData(0,0,i.width,i.height),document.body.append(i)}else B=A;const E=(D="start",o="string",d=(G=(G=["array","number","number","number","number","number","number","number","number","number"])||[]).every(function(i){return i==="number"}),o!=="string"&&d&&!a?QA(D):function(){return UA(D,o,G,arguments)});var D,o,G,a,d;await gA();const q=new Array(Math.ceil(B.data.length/32)).fill(0);for(let i=0;i<B.data.length;i+=4)if(C(B.data[i],B.data[i+1],B.data[i+2],B.data[i+3])){const w=Math.floor(i/4);q[Math.floor(w/8)]+=1<<w%8}const R=E(q,B.width,B.height,!0,I.pathonly,I.turdsize,I.turnpolicy,I.alphamax,I.opticurve,I.opttolerance);return I.pathonly?R.split("M").filter(i=>i).map(i=>"M"+i):R};function gA(){return new Promise(A=>{tA?A():g.onRuntimeInitialized=()=>{A()}})}if(typeof module<"u"){const A=gA;module.exports={potrace:nA,init:A}}const LA=async(A,I)=>(await gA(),await nA(A,I));self.addEventListener("message",async A=>{const{imageData:I,params:C}=A.data,B=await LA(I,C);A.ports[0].postMessage({result:B})})})();
